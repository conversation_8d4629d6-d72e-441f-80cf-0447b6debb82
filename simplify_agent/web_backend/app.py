#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Flask应用主文件
"""

import os
import sys
import logging
from flask import Flask

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from .config import config
from .middleware import setup_cors

# 导入数据库相关模块
try:
    from simplify_agent.database.db_connection import init_database_manager, DatabaseConfig
except ImportError as e:
    print(f"Warning: Could not import database modules: {e}")
    init_database_manager = None
    DatabaseConfig = None


def create_app(config_name: str = None) -> Flask:
    """创建Flask应用
    
    Args:
        config_name: 配置名称
        
    Returns:
        Flask应用实例
    """
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 设置日志
    logging.basicConfig(
        level=getattr(logging, app.config['LOG_LEVEL']),
        format=app.config['LOG_FORMAT']
    )
    
    # 设置CORS
    setup_cors(app)
    
    # 注册蓝图
    register_blueprints(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 创建静态文件目录
    create_static_directories(app)
    
    # 初始化数据库连接
    init_database(app)
    
    return app


def register_blueprints(app: Flask) -> None:
    """注册蓝图
    
    Args:
        app: Flask应用实例
    """
    from .api.user_api import user_bp
    from .api.dev_api import dev_bp
    from .api.statistics_api import statistics_bp
    
    # 注册API蓝图
    api_prefix = app.config.get('API_PREFIX', '/api')
    app.register_blueprint(user_bp, url_prefix=f'{api_prefix}/user')
    app.register_blueprint(dev_bp, url_prefix=f'{api_prefix}/dev')
    app.register_blueprint(statistics_bp, url_prefix=f'{api_prefix}/statistics')


def register_error_handlers(app: Flask) -> None:
    """注册错误处理器
    
    Args:
        app: Flask应用实例
    """
    from .utils.response import error_response, server_error_response
    
    @app.errorhandler(400)
    def bad_request(error):
        return error_response("请求参数错误", 400)
    
    @app.errorhandler(401)
    def unauthorized(error):
        return error_response("未授权访问", 401)
    
    @app.errorhandler(403)
    def forbidden(error):
        return error_response("禁止访问", 403)
    
    @app.errorhandler(404)
    def not_found(error):
        return error_response("接口不存在", 404)
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        return error_response("请求方法不允许", 405)
    
    @app.errorhandler(500)
    def internal_error(error):
        app.logger.error(f'服务器内部错误: {error}')
        return server_error_response()
    
    @app.errorhandler(Exception)
    def handle_exception(error):
        app.logger.error(f'未处理的异常: {error}')
        return server_error_response()


def create_static_directories(app: Flask) -> None:
    """创建静态文件目录
    
    Args:
        app: Flask应用实例
    """
    upload_folder = app.config.get('UPLOAD_FOLDER', 'static/uploads')
    screenshot_folder = app.config.get('SCREENSHOT_FOLDER', 'static/screenshots')
    
    for folder in [upload_folder, screenshot_folder]:
        os.makedirs(folder, exist_ok=True)


def init_database(app: Flask) -> None:
    """初始化数据库连接
    
    Args:
        app: Flask应用实例
    """
    if init_database_manager and DatabaseConfig:
        try:
            # 使用Flask配置中的数据库路径
            database_path = app.config.get('DATABASE_PATH', 'data/simplify_agent.db')

            # 确保路径是绝对路径
            if not os.path.isabs(database_path):
                # 如果是相对路径，相对于项目根目录解析
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                database_path = os.path.join(project_root, 'simplify_agent', 'data', 'simplify_agent.db')
            
            # 确保数据库目录存在
            db_dir = os.path.dirname(database_path)
            if not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)
            
            # 创建数据库配置
            db_config = DatabaseConfig(database_path=database_path)
            
            # 初始化数据库管理器
            db_manager = init_database_manager(db_config)
            
            app.logger.info(f"Database initialized: {database_path}")
            
            # 执行健康检查
            health = db_manager.health_check()
            if health['status'] == 'healthy':
                app.logger.info("Database health check passed")
                app.logger.info(f"Database tables: {health['database_info']['tables']}")
            else:
                app.logger.error(f"Database health check failed: {health.get('error', 'Unknown error')}")
                
        except Exception as e:
            app.logger.error(f"Failed to initialize database: {e}")
            import traceback
            app.logger.error(f"Database initialization traceback: {traceback.format_exc()}")
    else:
        app.logger.warning("Database modules not available, skipping database initialization")


# 创建应用实例
app = create_app()


if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV', 'default') == 'development'
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug,
        threaded=True
    )