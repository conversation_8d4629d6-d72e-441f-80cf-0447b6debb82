import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 3000,
    host: '0.0.0.0',
    allowedHosts: [
      'localhost',
      '127.0.0.1',
      '************',
      '.ngrok-free.app',  // 允许所有 ngrok-free.app 子域名
      '.ngrok.app',       // 支持付费版 ngrok 域名
      '.ngrok.io'         // 支持旧版 ngrok 域名
    ],
    proxy: {
      '/api': {
        target: 'http://************:5003',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'esbuild',
  },
})