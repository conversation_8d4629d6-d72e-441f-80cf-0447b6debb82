"""
SimplifyAgent 数据解析引擎

智能解析三种类型的日志文件并存储到数据库：
1. JSON计划文件 (test_plans表)
2. 执行日志文件 (test_executions + tool_executions表)
3. 评价报告文件 (comprehensive_evaluations + plan_evaluations + execution_evaluations表)

作者: SimplifyAgent Development Team
创建时间: 2024-09-04
"""

import os
import json
import re
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import traceback

# 添加项目根路径以导入数据库模块
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from server_logger import get_logger, PerformanceTimer
from config.log_server_config import LogServerConfig


class DataParser:
    """数据解析引擎"""
    
    def __init__(self, config: 'LogServerConfig'):
        """初始化数据解析器"""
        self.config = config
        self.logger = get_logger('log_server', 'data_parser')
        
        # 初始化数据库连接（延迟导入）
        self.db_connection = None
        self._initialize_database()
        
        # 解析器映射
        self.parsers = {
            'json_plan': self._parse_json_plan_file,
            'agent_execute': self._parse_agent_execute_folder,
            'judge_report': self._parse_judge_report_file
        }
        
        self.logger.info("数据解析引擎初始化完成")
    
    def _initialize_database(self):
        """初始化数据库连接"""
        try:
            # 动态导入数据库相关模块
            from data.database_manager import DatabaseManager
            
            db_config = self.config.get_database_config()
            self.db_manager = DatabaseManager(db_config['path'])
            
            # 测试连接
            if self.db_manager.test_connection():
                self.logger.info("数据库连接初始化成功")
            else:
                raise RuntimeError("数据库连接测试失败")
            
        except Exception as e:
            self.logger.error(f"数据库连接初始化失败: {e}")
            raise
    
    def process_file(self, file_path: str, watch_type: str) -> Dict[str, Any]:
        """
        处理文件的主入口
        
        Args:
            file_path: 文件路径
            watch_type: 监控类型 (json_plan, agent_execute, judge_report)
            
        Returns:
            处理结果字典
        """
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始解析文件: {file_path} (类型: {watch_type})")
            
            with PerformanceTimer(self.logger, f"parse_{watch_type}"):
                # 获取对应的解析器
                parser_func = self.parsers.get(watch_type)
                if not parser_func:
                    raise ValueError(f"未知的监控类型: {watch_type}")
                
                # 检查文件存在性
                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"文件不存在: {file_path}")
                
                # 执行解析
                result = parser_func(file_path)
                
                duration = (datetime.now() - start_time).total_seconds()
                
                if result.get('success', False):
                    self.logger.log_file_event(
                        'parsed', file_path,
                        success=True,
                        duration=duration
                    )
                    return {
                        'success': True,
                        'watch_type': watch_type,
                        'file_path': file_path,
                        'duration': duration,
                        'records_inserted': result.get('records_inserted', 0),
                        'message': result.get('message', '解析成功')
                    }
                else:
                    error_msg = result.get('error', '未知错误')
                    self.logger.log_file_event(
                        'parse_failed', file_path,
                        success=False,
                        duration=duration,
                        error_msg=error_msg
                    )
                    return {
                        'success': False,
                        'watch_type': watch_type,
                        'file_path': file_path,
                        'duration': duration,
                        'error': error_msg
                    }
                
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            error_msg = f"解析异常: {str(e)}"
            
            self.logger.log_file_event(
                'parse_error', file_path,
                success=False,
                duration=duration,
                error_msg=error_msg
            )
            
            return {
                'success': False,
                'watch_type': watch_type,
                'file_path': file_path,
                'duration': duration,
                'error': error_msg,
                'traceback': traceback.format_exc()
            }
    
    def _parse_json_plan_file(self, json_file_path: str) -> Dict[str, Any]:
        """解析JSON计划文件"""
        try:
            # 提取计划数据
            plan_data = self._extract_test_plan_data(json_file_path)
            
            # 存储到数据库
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 插入test_plans表
                insert_sql = """
                INSERT OR REPLACE INTO test_plans (
                    plan_id, original_request, platform, total_steps,
                    plan_summary, structured_plan, generation_metadata,
                    agent_instructions, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                cursor.execute(insert_sql, (
                    plan_data['plan_id'],
                    plan_data['original_request'],
                    plan_data['platform'],
                    plan_data['total_steps'],
                    plan_data['plan_summary'],
                    plan_data['structured_plan'],
                    plan_data['generation_metadata'],
                    plan_data['agent_instructions'],
                    plan_data['created_at'],
                    plan_data['updated_at']
                ))
                
                conn.commit()
                
                self.logger.log_database_operation(
                    'insert', 'test_plans',
                    success=True,
                    affected_rows=cursor.rowcount
                )
                
                return {
                    'success': True,
                    'records_inserted': 1,
                    'message': f'计划文件解析成功: {plan_data["plan_id"]}'
                }
                
        except Exception as e:
            self.logger.error(f"JSON计划文件解析失败: {json_file_path}, 错误: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _parse_agent_execute_folder(self, folder_path: str) -> Dict[str, Any]:
        """解析执行日志文件夹"""
        try:
            # 检查是否为执行日志文件夹
            if not os.path.isdir(folder_path):
                return {'success': False, 'error': '不是有效的文件夹'}
            
            folder_name = os.path.basename(folder_path)
            if not folder_name.startswith('round_'):
                return {'success': False, 'error': '不是执行日志文件夹'}
            
            # 提取执行数据
            execution_data = self._extract_execution_data(folder_path)
            tool_executions = self._extract_tool_executions(
                os.path.join(folder_path, 'task_structured.log'),
                execution_data['execution_id']
            )
            
            total_records = 0
            
            # 存储到数据库
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 插入test_executions表
                execution_sql = """
                INSERT OR REPLACE INTO test_executions (
                    execution_id, plan_id, original_request, execution_status,
                    total_rounds, total_duration, start_time, end_time, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                cursor.execute(execution_sql, (
                    execution_data['execution_id'],
                    execution_data['plan_id'],
                    execution_data['original_request'],
                    execution_data['execution_status'],
                    execution_data['total_rounds'],
                    execution_data['total_duration'],
                    execution_data['start_time'],
                    execution_data['end_time'],
                    execution_data['created_at']
                ))
                
                total_records += cursor.rowcount
                
                # 插入tool_executions表
                if tool_executions:
                    tool_sql = """
                    INSERT OR REPLACE INTO tool_executions (
                        execution_id, round_number, tool_name, tool_parameters,
                        execution_time, tool_status, tool_result, result_summary,
                        image_url, local_path, error_message, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    for tool_exec in tool_executions:
                        cursor.execute(tool_sql, (
                            tool_exec['execution_id'],
                            tool_exec['round_number'],
                            tool_exec['tool_name'],
                            tool_exec['tool_parameters'],
                            tool_exec['execution_time'],
                            tool_exec['tool_status'],
                            tool_exec['tool_result'],
                            tool_exec['result_summary'],
                            tool_exec['image_url'],
                            tool_exec['local_path'],
                            tool_exec['error_message'],
                            tool_exec['created_at']
                        ))
                    
                    total_records += len(tool_executions)
                
                conn.commit()
                
                self.logger.log_database_operation(
                    'insert', 'test_executions+tool_executions',
                    success=True,
                    affected_rows=total_records
                )
                
                return {
                    'success': True,
                    'records_inserted': total_records,
                    'message': f'执行日志解析成功: {execution_data["execution_id"]} ({len(tool_executions)}个工具执行)'
                }
                
        except Exception as e:
            self.logger.error(f"执行日志解析失败: {folder_path}, 错误: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _parse_judge_report_file(self, report_path: str) -> Dict[str, Any]:
        """解析评价报告文件 - 使用统一的test_evaluations表"""
        try:
            # 提取评价数据
            evaluation_data = self._extract_evaluation_data(report_path)

            if not evaluation_data:
                return {
                    'success': False,
                    'error': '评价数据提取失败'
                }

            total_records = 0
            test_point_records = 0

            # 存储到数据库
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # 插入test_evaluations表 (统一评价表)
                eval_sql = """
                INSERT OR REPLACE INTO test_evaluations (
                    execution_id, evaluation_round, analysis_time, analysis_model, round_number,
                    final_success_status, overall_success_score, confidence_score,
                    plan_quality_score, execution_compliance_score, execution_quality_score, goal_achievement_score,
                    original_instruction, test_plan_json, execution_summary_json,
                    plan_analysis_content, comprehensive_analysis,
                    major_issues_found, execution_highlights, improvement_priorities,
                    final_recommendations, key_findings, plan_coverage_assessment,
                    evaluation_summary, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                cursor.execute(eval_sql, (
                    evaluation_data['execution_id'],
                    evaluation_data['evaluation_round'],
                    evaluation_data['analysis_time'],
                    evaluation_data['analysis_model'],
                    evaluation_data['round_number'],
                    evaluation_data['final_success_status'],
                    evaluation_data['overall_success_score'],
                    evaluation_data['confidence_score'],
                    evaluation_data['plan_quality_score'],
                    evaluation_data['execution_compliance_score'],
                    evaluation_data['execution_quality_score'],
                    evaluation_data['goal_achievement_score'],
                    evaluation_data['original_instruction'],
                    evaluation_data['test_plan_json'],
                    evaluation_data['execution_summary_json'],
                    evaluation_data['plan_analysis_content'],
                    evaluation_data['comprehensive_analysis'],
                    evaluation_data['major_issues_found'],
                    evaluation_data['execution_highlights'],
                    evaluation_data['improvement_priorities'],
                    evaluation_data['final_recommendations'],
                    evaluation_data['key_findings'],
                    evaluation_data['plan_coverage_assessment'],
                    evaluation_data['evaluation_summary'],
                    evaluation_data['created_at']
                ))

                total_records += cursor.rowcount

                # 提取并插入测试点数据
                test_points = self._extract_test_point_data(report_path, evaluation_data['execution_id'])
                if test_points:
                    test_point_sql = """
                    INSERT OR REPLACE INTO test_point_evaluations (
                        execution_id, test_point_name, test_point_order, achieved, brief_analysis, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?)
                    """

                    for test_point in test_points:
                        cursor.execute(test_point_sql, (
                            test_point['execution_id'],
                            test_point['test_point_name'],
                            test_point['test_point_order'],
                            test_point['achieved'],
                            test_point['brief_analysis'],
                            test_point['created_at']
                        ))
                        test_point_records += cursor.rowcount

                conn.commit()

                self.logger.log_database_operation(
                    'insert', 'test_evaluations+test_point_evaluations',
                    success=True,
                    affected_rows=total_records + test_point_records
                )

                return {
                    'success': True,
                    'records_inserted': total_records + test_point_records,
                    'message': f'评价报告解析成功: {evaluation_data["execution_id"]} ({test_point_records}个测试点)'
                }

        except Exception as e:
            self.logger.error(f"评价报告解析失败: {report_path}, 错误: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    # ========== 数据提取函数 (从开发计划中移植) ==========
    
    def _extract_test_plan_data(self, json_file_path: str) -> Dict[str, Any]:
        """从JSON计划文件提取test_plans表数据"""
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 从文件名提取plan_id，而不是从文件内容中提取
        file_name = os.path.basename(json_file_path)
        plan_id = os.path.splitext(file_name)[0]  # 去掉.json后缀，如 "plan_mlx_20250904_165540"
        
        structured_plan = data.get('structured_plan', {})
        return {
            'plan_id': plan_id,
            'original_request': data.get('original_request'),
            'platform': structured_plan.get('platform', '').lower(),
            'total_steps': int(structured_plan.get('total_steps', 0)),
            'plan_summary': structured_plan.get('summary', '').strip(),
            'structured_plan': json.dumps(structured_plan, ensure_ascii=False),
            'generation_metadata': json.dumps(data.get('generation_result', {}), ensure_ascii=False),
            'agent_instructions': json.dumps(data.get('agent_instructions', {}), ensure_ascii=False),
            'created_at': datetime.fromisoformat(data.get('timestamp').replace('Z', '+00:00')),
            'updated_at': datetime.now()
        }
    
    def _extract_execution_data(self, folder_path: str) -> Dict[str, Any]:
        """从执行日志文件夹提取test_executions表数据"""
        execution_id = os.path.basename(folder_path)
        agent_log_path = os.path.join(folder_path, 'agent.log')
        task_log_path = os.path.join(folder_path, 'task_structured.log')

        # 从agent.log提取基本信息
        plan_id = self._extract_plan_id_from_agent_log(agent_log_path)
        original_request = self._extract_original_request_from_agent_log(agent_log_path)

        # 从task_structured.log提取执行统计
        execution_stats = self._analyze_task_structured_log_new_format(task_log_path)

        return {
            'execution_id': execution_id,
            'plan_id': plan_id,
            'original_request': original_request,
            'execution_status': execution_stats['final_status'],
            'total_rounds': execution_stats['total_rounds'],
            'total_duration': execution_stats['duration_seconds'],
            'start_time': execution_stats['start_time'],
            'end_time': execution_stats['end_time'],
            'created_at': datetime.now()
        }
    
    def _extract_plan_id_from_agent_log(self, agent_log_path: str) -> str:
        """从agent.log中提取plan_id（完整文件名）"""
        try:
            with open(agent_log_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找完整的计划文件路径，提取文件名主体作为plan_id
            # 匹配模式：simplify_agent/log/json_plan/plan_mlx_20250904_165540.json
            plan_match = re.search(r'simplify_agent/log/json_plan/(plan_mlx_\w+)\.json', content)
            if plan_match:
                return plan_match.group(1)  # 返回不含后缀的文件名，如 "plan_mlx_20250904_165540"
            
            # 备用匹配模式：直接匹配文件名部分
            plan_match = re.search(r'(plan_mlx_\w+)\.json', content)
            if plan_match:
                return plan_match.group(1)
            
            return "unknown_plan"
        except Exception:
            return "unknown_plan"
    
    def _extract_original_request_from_agent_log(self, agent_log_path: str) -> str:
        """从agent.log中提取原始请求"""
        try:
            with open(agent_log_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 查找原始请求内容 - 支持多种格式
            patterns = [
                r'原始用户请求[：:]\s*(.+?)(?=\n.*?收到用户输入|\n.*?─|\n.*?🎯|\n.*?📋|$)',  # 新格式
                r'📝 原始用户请求[：:]\s*(.+?)(?=\n.*?收到用户输入|\n.*?─|\n.*?🎯|\n.*?📋|$)',  # 新格式变体
                r'原始请求[：:]\s*(.+?)(?=\n|\r|$)',  # 旧格式
                r'用户请求[：:]\s*(.+?)(?=\n|\r|$)',  # 备用格式
            ]

            for pattern in patterns:
                request_match = re.search(pattern, content, re.DOTALL)
                if request_match:
                    return request_match.group(1).strip()

            return "未找到原始请求"
        except Exception:
            return "解析失败"
    
    def _analyze_task_structured_log_new_format(self, log_path: str) -> Dict[str, Any]:
        """分析新格式的task_structured.log获取执行统计信息"""
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception:
            # 如果文件不存在或读取失败，返回默认值
            return {
                'final_status': '未知',
                'total_rounds': 0,
                'duration_seconds': 0.0,
                'start_time': datetime.now(),
                'end_time': datetime.now()
            }

        # 提取所有执行轮次 - 新格式使用 "─── 第X轮执行 ───"
        rounds = re.findall(r'─── 第(\d+)轮执行 ───', content)
        total_rounds = len(rounds)

        # 提取最终状态 - 从任务完成部分获取
        final_status = 'failed'  # 默认状态

        # 从任务完成部分提取状态
        if '任务完成' in content and '执行状态: 成功' in content:
            final_status = 'success'
        elif '超时' in content:
            final_status = 'timeout'
        elif '取消' in content or '中断' in content:
            final_status = 'cancelled'
        elif '成功' in content and '失败' not in content:
            final_status = 'success'

        # 提取总耗时（从最后的统计信息中）
        duration_match = re.search(r'总耗时[：:]?\s*([\d.]+)秒', content)
        duration = float(duration_match.group(1)) if duration_match else 0.0

        # 提取开始和结束时间
        start_time_match = re.search(r'开始时间[：:]?\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', content)
        end_time_match = re.search(r'结束时间[：:]?\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', content)

        try:
            start_time = datetime.strptime(start_time_match.group(1), '%Y-%m-%d %H:%M:%S') if start_time_match else datetime.now()
            end_time = datetime.strptime(end_time_match.group(1), '%Y-%m-%d %H:%M:%S') if end_time_match else datetime.now()
        except ValueError:
            start_time = datetime.now()
            end_time = datetime.now()

        return {
            'final_status': final_status,
            'total_rounds': total_rounds,
            'duration_seconds': duration,
            'start_time': start_time,
            'end_time': end_time
        }
    
    def _extract_tool_executions(self, task_log_path: str, execution_id: str) -> List[Dict[str, Any]]:
        """从新格式的task_structured.log提取所有工具执行记录"""
        try:
            with open(task_log_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception:
            return []

        tool_executions = []

        # 分割每个执行轮次 - 新格式
        round_sections = re.split(r'─── 第(\d+)轮执行 ───', content)[1:]
        
        for i in range(0, len(round_sections), 2):
            if i + 1 >= len(round_sections):
                break

            round_num = int(round_sections[i])
            section = round_sections[i + 1]

            # 检查是否是特殊的测试报告轮次（模型决定直接回复用户）
            if '模型决定: 直接回复用户' in section:
                # 处理测试报告类型的轮次
                tool_name = 'process_report'
                tool_params = '{}'
                exec_time = 0.0
                status = 'success'
                image_url = ''
                local_path = ''

                # 提取最终回复内容
                final_reply_match = re.search(r'最终回复开始:(.*?)最终回复结束', section, re.DOTALL)
                if final_reply_match:
                    raw_content = final_reply_match.group(1).strip()

                    # 提取纯净的回复内容，移除日志格式信息
                    content_lines = []
                    for line in raw_content.split('\n'):
                        # 移除日志时间戳和格式前缀，只保留实际内容
                        # 匹配格式：2025-09-06 10:15:06 - INFO - [Task_5629910480] -         内容
                        clean_line = re.sub(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} - \w+ - \[.*?\] -\s*', '', line)
                        if clean_line.strip():  # 只保留非空行
                            content_lines.append(clean_line.strip())

                    # 重新组合内容，保持原有的换行结构
                    final_reply_content = '\n'.join(content_lines)

                    # 构造JSON格式的工具结果
                    tool_result = json.dumps({
                        'status': 'success',
                        'report_type': 'process_report',
                        'final_reply': final_reply_content,
                        'message': '流程报告生成完成'
                    }, ensure_ascii=False)
                    summary = final_reply_content[:100] + '...' if len(final_reply_content) > 100 else final_reply_content
                else:
                    # 如果没有找到最终回复，使用整个section作为内容
                    tool_result = json.dumps({
                        'status': 'success',
                        'report_type': 'process_report',
                        'final_reply': section.strip(),
                        'message': '流程报告生成完成'
                    }, ensure_ascii=False)
                    summary = '流程报告生成完成'
            else:
                # 标准工具执行的处理逻辑 - 适配新格式
                tool_name = self._extract_by_regex(section, r'执行工具[：:]?\s*(\w+)', 'unknown')
                tool_params = self._extract_by_regex(section, r'工具参数[：:]?\s*({.*?})', '{}')
                exec_time = float(self._extract_by_regex(section, r'执行耗时[：:]?\s*([\d.]+)秒', '0'))
                tool_result = self._extract_by_regex(section, r'执行结果[：:]?\s*({.*?})', '{}')
                summary = self._extract_by_regex(section, r'工具执行结果[：:]?\s*([^\n]+)', '').strip()

                # 优先从"工具运行状况"这个固定格式中提取状态
                status_from_log = self._extract_by_regex(section, r'工具运行状况[：:]?\s*(\w+)', '')

                # 解析工具结果获取附加信息
                try:
                    result_json = json.loads(tool_result)
                    image_url = result_json.get('image_url', '')
                    local_path = result_json.get('local_path', '')

                    # 如果日志中没有找到状态，则从JSON结果中获取
                    if not status_from_log:
                        status = (result_json.get('status') or
                                result_json.get('execution_status') or
                                'unknown')
                    else:
                        status = status_from_log

                except json.JSONDecodeError:
                    # 如果JSON解析失败，使用日志中的状态或默认值
                    status = status_from_log if status_from_log else 'parse_error'
                    image_url = ''
                    local_path = ''

            # 状态值映射：确保符合数据库约束
            if status not in ['success', 'error', 'timeout', 'cancelled']:
                if status in ['unknown', 'parse_error']:
                    status = None  # 设置为 NULL，符合数据库约束
            
            tool_executions.append({
                'execution_id': execution_id,
                'round_number': round_num,
                'tool_name': tool_name,
                'tool_parameters': tool_params,
                'execution_time': exec_time,
                'tool_status': status,
                'tool_result': tool_result,
                'result_summary': summary,
                'image_url': image_url,
                'local_path': local_path,
                'error_message': result_json.get('message', '') if status == 'error' else '',
                'created_at': datetime.now()
            })
        
        return tool_executions
    
    def _extract_evaluation_data(self, report_path: str) -> Dict[str, Any]:
        """从新格式的JSON评价报告提取统一评价表数据"""
        try:
            with open(report_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 从文件名提取execution_id
            filename = os.path.basename(report_path)
            execution_id = filename.replace('_report.json', '').replace('_report.log', '')

            return self._extract_unified_evaluation_new_format(data, execution_id)

        except json.JSONDecodeError:
            # 如果不是JSON格式，尝试按旧格式解析
            with open(report_path, 'r', encoding='utf-8') as f:
                content = f.read()
            filename = os.path.basename(report_path)
            execution_id = filename.replace('_report.log', '')
            return self._extract_unified_evaluation_old_format(content, execution_id)
        except Exception as e:
            self.logger.error(f"提取评价数据失败: {report_path}, 错误: {e}")
            return {}

    def _extract_unified_evaluation_new_format(self, data: Dict[str, Any], execution_id: str) -> Dict[str, Any]:
        """从新格式JSON数据提取统一评价表数据"""
        metadata = data.get('metadata', {})
        test_info = data.get('test_info', {})
        structured_eval = data.get('structured_evaluation', {})
        eval_summary = structured_eval.get('evaluation_summary', {})
        dimension_scores = structured_eval.get('dimension_scores', {})

        consolidated_analysis = structured_eval.get('consolidated_analysis', {})

        # 提取测试计划信息
        test_plan = test_info.get('test_plan', {})
        execution_summary = test_info.get('execution_summary', {})

        # 状态值映射（中文到英文）
        status_mapping = {
            '成功': 'success',
            '失败': 'failed',
            '部分成功': 'partial',
            '未知': 'unknown',
            'success': 'success',
            'failed': 'failed',
            'partial': 'partial',
            'unknown': 'unknown'
        }

        raw_status = eval_summary.get('overall_success', '')
        final_status = status_mapping.get(raw_status, 'unknown')

        return {
            'execution_id': execution_id,
            'evaluation_round': metadata.get('round_name', ''),
            'analysis_time': self._parse_datetime_iso(metadata.get('timestamp', '')),
            'analysis_model': metadata.get('analysis_model', ''),
            'round_number': metadata.get('round_number', 0),

            # 总体评价结果
            'final_success_status': final_status,
            'overall_success_score': eval_summary.get('overall_score', 0.0),
            'confidence_score': eval_summary.get('confidence_level', 0.0),

            # 维度评分
            'plan_quality_score': dimension_scores.get('plan_quality_score', 0.0),
            'execution_compliance_score': dimension_scores.get('execution_compliance_score', 0.0),
            'execution_quality_score': dimension_scores.get('execution_quality_score', 0.0),
            'goal_achievement_score': dimension_scores.get('goal_achievement_score', 0.0),

            # 测试信息
            'original_instruction': test_info.get('original_instruction', ''),
            'test_plan_json': json.dumps(test_plan, ensure_ascii=False) if test_plan else None,
            'execution_summary_json': json.dumps(execution_summary, ensure_ascii=False) if execution_summary else None,

            # 分析内容
            'plan_analysis_content': consolidated_analysis.get('plan_coverage_assessment', ''),
            'comprehensive_analysis': json.dumps(consolidated_analysis, ensure_ascii=False),

            # 问题和建议
            'major_issues_found': json.dumps(consolidated_analysis.get('major_issues_found', []), ensure_ascii=False),
            'execution_highlights': consolidated_analysis.get('execution_highlights', ''),
            'improvement_priorities': json.dumps(consolidated_analysis.get('improvement_priorities', []), ensure_ascii=False),
            'final_recommendations': json.dumps(structured_eval.get('final_recommendations', []), ensure_ascii=False),
            'key_findings': json.dumps(eval_summary.get('key_findings', []), ensure_ascii=False),

            # 综合分析
            'plan_coverage_assessment': consolidated_analysis.get('plan_coverage_assessment', ''),

            # 评价总结
            'evaluation_summary': json.dumps(eval_summary, ensure_ascii=False),

            'created_at': datetime.now()
        }

    def _extract_unified_evaluation_old_format(self, content: str, execution_id: str) -> Dict[str, Any]:
        """从旧格式文本数据提取统一评价表数据（向后兼容）"""
        return {
            'execution_id': execution_id,
            'evaluation_round': execution_id,
            'analysis_time': datetime.now(),
            'analysis_model': 'unknown',
            'round_number': 0,
            'final_success_status': 'unknown',
            'overall_success_score': 0.0,
            'confidence_score': 0.0,
            'plan_quality_score': 0.0,
            'execution_compliance_score': 0.0,
            'execution_quality_score': 0.0,
            'goal_achievement_score': 0.0,
            'original_instruction': '',
            'comprehensive_analysis': content[:1000] if content else '',  # 截取前1000字符
            'evaluation_summary': '旧格式数据，内容有限',
            'created_at': datetime.now()
        }

    def _extract_test_point_data(self, report_path: str, execution_id: str) -> List[Dict[str, Any]]:
        """提取测试点数据"""
        try:
            with open(report_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            structured_eval = data.get('structured_evaluation', {})
            test_point_analysis = structured_eval.get('test_point_analysis', {})

            if not test_point_analysis:
                return []

            identified_test_points = test_point_analysis.get('identified_test_points', [])
            achievement_details = test_point_analysis.get('achievement_details', {})

            test_points = []
            for i, test_point_name in enumerate(identified_test_points):
                test_point_detail = achievement_details.get(test_point_name, {})

                test_points.append({
                    'execution_id': execution_id,
                    'test_point_name': test_point_name,
                    'test_point_order': i + 1,
                    'achieved': test_point_detail.get('achieved', False),
                    'brief_analysis': test_point_detail.get('brief_analysis', ''),
                    'created_at': datetime.now()
                })

            return test_points

        except (json.JSONDecodeError, Exception) as e:
            self.logger.warning(f"提取测试点数据失败: {report_path}, 错误: {e}")
            return []

    def _extract_comprehensive_evaluation_new_format(self, data: Dict[str, Any], execution_id: str) -> Dict[str, Any]:
        """从新格式JSON数据提取综合评价数据"""
        metadata = data.get('metadata', {})
        structured_eval = data.get('structured_evaluation', {})
        eval_summary = structured_eval.get('evaluation_summary', {})
        comprehensive_eval = structured_eval.get('comprehensive_evaluation', {})

        return {
            'execution_id': execution_id,
            'evaluation_round': metadata.get('round_name', ''),
            'analysis_time': self._parse_datetime_iso(metadata.get('timestamp', '')),
            'analysis_model': metadata.get('analysis_model', ''),
            'final_success_status': eval_summary.get('overall_success', ''),
            'overall_success_score': eval_summary.get('overall_score', 0.0),
            'confidence_score': eval_summary.get('confidence_level', 0.0),
            'comprehensive_analysis': json.dumps(comprehensive_eval, ensure_ascii=False),
            'evaluation_summary': json.dumps(eval_summary, ensure_ascii=False),
            'next_steps_recommendations': json.dumps(comprehensive_eval.get('recommendations', []), ensure_ascii=False),
            'created_at': datetime.now()
        }

    def _extract_plan_evaluation_new_format(self, data: Dict[str, Any], execution_id: str) -> Dict[str, Any]:
        """从新格式JSON数据提取计划评价数据"""
        structured_eval = data.get('structured_evaluation', {})
        dimension_scores = structured_eval.get('dimension_scores', {})
        detailed_analysis = structured_eval.get('detailed_analysis', {})
        plan_analysis = detailed_analysis.get('plan_quality_analysis', {})

        return {
            'execution_id': execution_id,
            'plan_quality_score': dimension_scores.get('plan_quality_score', 0.0),
            'plan_analysis_content': plan_analysis.get('analysis_content', ''),
            'plan_key_issues': json.dumps(plan_analysis.get('key_issues', []), ensure_ascii=False),
            'plan_improvement_suggestions': json.dumps(plan_analysis.get('improvement_suggestions', []), ensure_ascii=False),
            'created_at': datetime.now()
        }

    def _extract_execution_evaluation_new_format(self, data: Dict[str, Any], execution_id: str) -> Dict[str, Any]:
        """从新格式JSON数据提取执行评价数据"""
        structured_eval = data.get('structured_evaluation', {})
        dimension_scores = structured_eval.get('dimension_scores', {})
        detailed_analysis = structured_eval.get('detailed_analysis', {})

        compliance_analysis = detailed_analysis.get('execution_compliance_analysis', {})
        quality_analysis = detailed_analysis.get('execution_quality_analysis', {})

        return {
            'execution_id': execution_id,
            'execution_compliance_score': dimension_scores.get('execution_compliance_score', 0.0),
            'execution_quality_score': dimension_scores.get('execution_quality_score', 0.0),
            'goal_achievement_score': dimension_scores.get('goal_achievement_score', 0.0),
            'compliance_analysis_content': compliance_analysis.get('analysis_content', ''),
            'execution_key_issues': json.dumps(quality_analysis.get('key_issues', []), ensure_ascii=False),
            'execution_improvement_suggestions': json.dumps(quality_analysis.get('improvement_suggestions', []), ensure_ascii=False),
            'created_at': datetime.now()
        }

    # 旧格式兼容方法
    def _extract_comprehensive_evaluation_old_format(self, content: str, execution_id: str) -> Dict[str, Any]:
        """提取旧格式综合评价数据"""
        return {
            'execution_id': execution_id,
            'evaluation_round': self._extract_number(content, r'🔢 轮次编号[：:]?\s*(\d+)'),
            'analysis_time': self._parse_datetime(self._extract_text(content, r'⏰ 分析时间[：:]?\s*([\d-]+ [\d:]+)')),
            'analysis_model': self._extract_text(content, r'🤖 分析模型[：:]?\s*(.+)'),
            'final_success_status': self._extract_text(content, r'\*\*最终成功状态\*\*[：:]?\s*(.+)'),
            'overall_success_score': self._extract_float(content, r'\*\*成功评分\*\*[：:]?\s*([\d.]+)'),
            'confidence_score': self._extract_float(content, r'\*\*置信度\*\*[：:]?\s*([\d.]+)'),
            'comprehensive_analysis': self._extract_section(content, r'## 3\.\s*综合评价与建议(.+?)(?=\n={40,}|$)'),
            'evaluation_summary': self._extract_section(content, r'## 总体评价结果(.+?)(?=##|$)'),
            'next_steps_recommendations': self._extract_section(content, r'\*\*改进方向\*\*[：:]?(.+?)(?=\n\n|$)'),
            'created_at': datetime.now()
        }

    def _extract_plan_evaluation_old_format(self, content: str, execution_id: str) -> Dict[str, Any]:
        """提取旧格式计划评价数据"""
        return {
            'execution_id': execution_id,
            'plan_quality_score': self._extract_float(content, r'\*\*测试计划质量评分\*\*[：:]?\s*([\d.]+)'),
            'plan_analysis_content': self._extract_section(content, r'## 1\.\s*测试计划质量分析(.+?)(?=##|$)'),
            'plan_key_issues': self._extract_section(content, r'然而，存在一些关键问题[：:]?(.+?)(?=\*\*改进建议\*\*|$)'),
            'plan_improvement_suggestions': self._extract_section(content, r'1\.\s*统一工具参数命名规范(.+?)(?=##|$)'),
            'created_at': datetime.now()
        }

    def _extract_execution_evaluation_old_format(self, content: str, execution_id: str) -> Dict[str, Any]:
        """提取旧格式执行评价数据"""
        return {
            'execution_id': execution_id,
            'execution_compliance_score': self._extract_float(content, r'\*\*执行符合度评分\*\*[：:]?\s*([\d.]+)'),
            'execution_quality_score': self._extract_float(content, r'\*\*执行质量评分\*\*[：:]?\s*([\d.]+)'),
            'goal_achievement_score': self._extract_float(content, r'\*\*目标达成度评分\*\*[：:]?\s*([\d.]+)'),
            'compliance_analysis_content': self._extract_section(content, r'## 2\.\s*执行符合度分析(.+?)(?=##|$)'),
            'execution_key_issues': self._extract_section(content, r'- \*\*工具调用错误\*\*[：:]?(.+?)(?=\*\*改进建议\*\*|$)'),
            'execution_improvement_suggestions': self._extract_section(content, r'1\.\s*建立工具参数校验机制(.+?)(?=##|$)'),
            'created_at': datetime.now()
        }

    # ========== 辅助提取函数 ==========
    
    def _extract_text(self, content: str, pattern: str) -> str:
        """提取文本"""
        match = re.search(pattern, content, re.DOTALL)
        return match.group(1).strip() if match else ""
    
    def _extract_number(self, content: str, pattern: str) -> int:
        """提取整数"""
        match = re.search(pattern, content)
        return int(match.group(1)) if match else 0
    
    def _extract_float(self, content: str, pattern: str) -> float:
        """提取浮点数"""
        match = re.search(pattern, content)
        try:
            return float(match.group(1)) if match else 0.0
        except ValueError:
            return 0.0
    
    def _extract_section(self, content: str, pattern: str) -> str:
        """提取章节内容，清理格式"""
        match = re.search(pattern, content, re.DOTALL)
        if match:
            section = match.group(1).strip()
            # 清理多余空行，保持结构
            return re.sub(r'\n\s*\n\s*\n', '\n\n', section)
        return ""
    
    def _parse_datetime(self, datetime_str: str) -> datetime:
        """解析日期时间字符串"""
        try:
            return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return datetime.now()

    def _parse_datetime_iso(self, datetime_str: str) -> datetime:
        """解析ISO格式的日期时间字符串"""
        try:
            # 处理带微秒的ISO格式
            if '.' in datetime_str:
                return datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            else:
                return datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
        except (ValueError, AttributeError):
            return datetime.now()
    
    def _extract_by_regex(self, text: str, pattern: str, default: str = '') -> str:
        """通用正则提取辅助函数"""
        match = re.search(pattern, text, re.DOTALL)
        return match.group(1) if match else default
    
    def get_stats(self) -> Dict[str, Any]:
        """获取解析器统计信息"""
        return {
            'parsers_available': list(self.parsers.keys()),
            'database_connected': self.db_manager is not None,
            'config_loaded': self.config is not None
        }


if __name__ == '__main__':
    # 数据解析器测试
    from config import get_config
    
    def test_data_parser():
        config = get_config()
        parser = DataParser(config)
        
        print("=== 数据解析器测试 ===")
        print(f"解析器统计: {parser.get_stats()}")
        
        # 测试JSON计划文件解析
        test_json_path = "/Users/<USER>/Desktop/work/langchain_ollama/simplify_agent/log/json_plan/plan_mlx_20250904_165540.json"
        if os.path.exists(test_json_path):
            result = parser.process_file(test_json_path, 'json_plan')
            print(f"JSON计划解析结果: {result}")
        
        # 测试评价报告解析
        test_report_path = "/Users/<USER>/Desktop/work/langchain_ollama/simplify_agent/log/judge_report/round_000006_20250904_171233_report.log"
        if os.path.exists(test_report_path):
            result = parser.process_file(test_report_path, 'judge_report')
            print(f"评价报告解析结果: {result}")
    
    test_data_parser()