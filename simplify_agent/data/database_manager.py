"""
SimplifyAgent 数据库管理器

整合现有的DAO层，为日志服务器提供统一的数据库访问接口

作者: SimplifyAgent Development Team
创建时间: 2024-09-04
"""

import sqlite3
import os
import sys
from contextlib import contextmanager
from typing import Optional

# 添加项目路径以导入DAO模块
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str):
        """初始化数据库管理器"""
        self.db_path = db_path
        
        # 确保数据库文件存在
        self._ensure_database_exists()
    
    def _ensure_database_exists(self):
        """确保数据库文件和表结构存在"""
        if not os.path.exists(self.db_path):
            # 创建目录
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            # 创建数据库和表
            try:
                from database.init_database import init_database
                init_database(self.db_path)
            except ImportError:
                # 如果init_database不存在，创建基本连接
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("SELECT 1")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 启用字典式访问
            yield conn
        finally:
            if conn:
                conn.close()
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                # 如果没有表，使用已存在的数据库
                if len(tables) == 0:
                    # 尝试使用主数据库
                    alt_paths = [
                        'simplify_agent/data/simplify_agent.db',
                        'data/simplify_agent.db'
                    ]
                    for alt_path in alt_paths:
                        if os.path.exists(alt_path):
                            self.db_path = alt_path
                            return self.test_connection()
                return True
        except Exception as e:
            print(f"数据库连接测试异常: {e}")
            return False