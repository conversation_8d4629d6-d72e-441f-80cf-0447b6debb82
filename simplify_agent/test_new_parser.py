#!/usr/bin/env python3
"""
测试新版本数据解析器
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根路径
sys.path.insert(0, os.path.dirname(__file__))

from config import get_config
from parsers.data_parser import DataParser


def test_new_judge_report_format():
    """测试新格式的judge_report解析"""
    print("=== 测试新格式 Judge Report 解析 ===")

    # 测试文件路径 - 使用最新的格式
    test_file = "simplify_agent/log/judge_report/round_000010_20250905_195230_report.json"

    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return False

    try:
        config = get_config()
        parser = DataParser(config)

        # 解析文件
        result = parser.process_file(test_file, 'judge_report')

        print(f"✅ 解析结果: {result}")
        return result.get('success', False)

    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_new_agent_execute_format():
    """测试新格式的agent_execute_log解析"""
    print("\n=== 测试新格式 Agent Execute Log 解析 ===")
    
    # 测试文件夹路径
    test_folder = "simplify_agent/log/agent_execute_log/round_000010_20250905_195230"
    
    if not os.path.exists(test_folder):
        print(f"❌ 测试文件夹不存在: {test_folder}")
        return False
    
    try:
        config = get_config()
        parser = DataParser(config)
        
        # 解析文件夹
        result = parser.process_file(test_folder, 'agent_execute')
        
        print(f"✅ 解析结果: {result}")
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_json_structure():
    """测试JSON结构解析"""
    print("\n=== 测试JSON结构解析 ===")

    test_file = "simplify_agent/log/judge_report/round_000010_20250905_195230_report.json"

    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return False

    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print("📋 JSON结构分析:")
        print(f"  - metadata: {list(data.get('metadata', {}).keys())}")
        print(f"  - test_info: {list(data.get('test_info', {}).keys())}")
        print(f"  - raw_analysis: {list(data.get('raw_analysis', {}).keys())}")
        print(f"  - structured_evaluation: {list(data.get('structured_evaluation', {}).keys())}")

        structured_eval = data.get('structured_evaluation', {})
        if structured_eval:
            print(f"  - evaluation_summary: {list(structured_eval.get('evaluation_summary', {}).keys())}")
            print(f"  - dimension_scores: {list(structured_eval.get('dimension_scores', {}).keys())}")
            print(f"  - test_point_analysis: {list(structured_eval.get('test_point_analysis', {}).keys())}")
            print(f"  - consolidated_analysis: {list(structured_eval.get('consolidated_analysis', {}).keys())}")

            # 测试点分析详情
            test_point_analysis = structured_eval.get('test_point_analysis', {})
            if test_point_analysis:
                identified_points = test_point_analysis.get('identified_test_points', [])
                print(f"  - 识别的测试点数量: {len(identified_points)}")
                if identified_points:
                    print(f"  - 测试点示例: {identified_points[0]}")

        return True

    except Exception as e:
        print(f"❌ JSON结构分析失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试新版本数据解析器")
    
    # 测试JSON结构
    json_ok = test_json_structure()
    
    # 测试judge_report解析
    judge_ok = test_new_judge_report_format()
    
    # 测试agent_execute解析
    agent_ok = test_new_agent_execute_format()
    
    print(f"\n📊 测试结果总结:")
    print(f"  - JSON结构解析: {'✅' if json_ok else '❌'}")
    print(f"  - Judge Report解析: {'✅' if judge_ok else '❌'}")
    print(f"  - Agent Execute解析: {'✅' if agent_ok else '❌'}")
    
    if all([json_ok, judge_ok, agent_ok]):
        print("\n🎉 所有测试通过！新版本数据解析器工作正常。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试。")


if __name__ == '__main__':
    main()
