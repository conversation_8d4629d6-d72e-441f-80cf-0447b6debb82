#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SimplifyAgent 数据库完整使用示例
演示如何使用数据库系统存储和查询测试数据
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from simplify_agent.database.init_database import initialize_simplify_agent_database, DatabaseInitializer
from simplify_agent.database.db_connection import DatabaseConfig, get_database_manager
from tools._concurrent_log_manager import get_current_task_log_manager


class DatabaseExample:
    """数据库使用示例类"""
    
    def __init__(self, database_path: str = 'simplify_agent/data/simplify_agent.db'):
        self.database_path = database_path
        self.config = DatabaseConfig(database_path=database_path)
        self.db_manager = None
        
    def run_complete_example(self) -> Dict[str, Any]:
        """运行完整的数据库使用示例"""
        
        example_result = {
            'steps_completed': [],
            'data_inserted': {},
            'queries_executed': [],
            'errors': []
        }
        
        try:
            print("🎯 SimplifyAgent 数据库完整使用示例")
            print("=" * 50)
            
            # 步骤1: 初始化数据库
            print("\n📝 步骤 1: 初始化数据库")
            init_result = self._initialize_database()
            example_result['steps_completed'].append('database_initialization')
            if init_result['status'] != 'success':
                raise Exception(f"数据库初始化失败: {init_result}")
            print(f"✅ 数据库初始化成功，创建了 {init_result['tables_created']} 个表")
            
            # 步骤2: 插入测试计划数据
            print("\n📝 步骤 2: 插入测试计划数据")
            plan_data = self._create_sample_plan_data()
            plan_id = self._insert_test_plan(plan_data)
            example_result['data_inserted']['test_plan'] = plan_id
            example_result['steps_completed'].append('insert_test_plan')
            print(f"✅ 插入测试计划成功，Plan ID: {plan_id}")
            
            # 步骤3: 插入测试执行数据
            print("\n📝 步骤 3: 插入测试执行数据")
            execution_data = self._create_sample_execution_data(plan_data['plan_id'])
            execution_id = self._insert_test_execution(execution_data)
            example_result['data_inserted']['test_execution'] = execution_id
            example_result['steps_completed'].append('insert_test_execution')
            print(f"✅ 插入测试执行成功，Execution ID: {execution_id}")
            
            # 步骤4: 插入工具执行详情
            print("\n📝 步骤 4: 插入工具执行详情")
            tool_count = self._insert_sample_tool_executions(execution_data['execution_id'])
            example_result['data_inserted']['tool_executions'] = tool_count
            example_result['steps_completed'].append('insert_tool_executions')
            print(f"✅ 插入工具执行详情成功，共 {tool_count} 个工具调用")
            
            # 步骤5: 插入评价数据
            print("\n📝 步骤 5: 插入评价数据")
            evaluation_count = self._insert_sample_evaluations(execution_data['execution_id'], plan_data['plan_id'])
            example_result['data_inserted']['evaluations'] = evaluation_count
            example_result['steps_completed'].append('insert_evaluations')
            print(f"✅ 插入评价数据成功，共 {evaluation_count} 个评价记录")
            
            # 步骤6: 执行查询示例
            print("\n📝 步骤 6: 执行查询示例")
            query_results = self._execute_sample_queries(execution_data['execution_id'])
            example_result['queries_executed'] = list(query_results.keys())
            example_result['steps_completed'].append('execute_queries')
            print(f"✅ 执行查询示例成功，共 {len(query_results)} 个查询")
            
            # 步骤7: 展示查询结果
            print("\n📝 步骤 7: 展示查询结果")
            self._display_query_results(query_results)
            example_result['steps_completed'].append('display_results')
            print("✅ 查询结果展示完成")
            
            # 步骤8: 数据库统计信息
            print("\n📝 步骤 8: 数据库统计信息")
            stats = self._get_database_statistics()
            example_result['database_stats'] = stats
            example_result['steps_completed'].append('database_stats')
            print("✅ 数据库统计信息获取完成")
            
            print(f"\n🎉 完整示例运行成功！完成了 {len(example_result['steps_completed'])} 个步骤")
            return example_result
            
        except Exception as e:
            error_msg = f"示例运行失败: {e}"
            example_result['errors'].append(error_msg)
            get_current_task_log_manager().error_tools(error_msg, "DatabaseExample")
            print(f"\n❌ {error_msg}")
            return example_result
    
    def _initialize_database(self) -> Dict[str, Any]:
        """初始化数据库"""
        # 删除现有数据库文件（重新开始）
        if os.path.exists(self.database_path):
            os.remove(self.database_path)
            
        return initialize_simplify_agent_database(
            database_path=self.database_path,
            force_recreate=True
        )
    
    def _create_sample_plan_data(self) -> Dict[str, Any]:
        """创建示例测试计划数据"""
        return {
            'plan_id': 'demo_plan_12345',
            'original_request': '点击左上角的地址，进入地址选择页后，校验地址选择页搜索框默认文案是否为"搜索城市/区县/地点"。',
            'platform': 'ios',
            'total_steps': 10,
            'plan_summary': '验证美团地址选择流程的测试计划示例',
            'structured_plan': json.dumps({
                'plan_id': 'demo_plan_12345',
                'steps': [
                    {
                        'step_id': 1,
                        'action': 'find_available_device',
                        'description': '查找可用的iOS设备',
                        'parameters': {'platform': 'ios'}
                    },
                    {
                        'step_id': 2, 
                        'action': 'start_device_test',
                        'description': '启动设备测试会话',
                        'parameters': {'udid': '{device_udid}'}
                    }
                ]
            }, ensure_ascii=False),
            'generation_metadata': json.dumps({
                'processing_time': 15.5,
                'model_info': {
                    'framework': 'MLX',
                    'model_path': 'Qwen3-30B-Demo',
                    'temperature': 0.0
                }
            }, ensure_ascii=False),
            'agent_instructions': '请按照计划步骤执行测试，确保每步完成后再进行下一步。'
        }
    
    def _insert_test_plan(self, plan_data: Dict[str, Any]) -> int:
        """插入测试计划数据"""
        if not self.db_manager:
            self.db_manager = get_database_manager(self.config)
            
        insert_sql = """
        INSERT INTO test_plans (
            plan_id, original_request, platform, total_steps, plan_summary,
            structured_plan, generation_metadata, agent_instructions
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            plan_data['plan_id'],
            plan_data['original_request'],
            plan_data['platform'],
            plan_data['total_steps'],
            plan_data['plan_summary'],
            plan_data['structured_plan'],
            plan_data['generation_metadata'],
            plan_data['agent_instructions']
        )
        
        return self.db_manager.execute_update(insert_sql, params)
    
    def _create_sample_execution_data(self, plan_id: str) -> Dict[str, Any]:
        """创建示例测试执行数据"""
        return {
            'execution_id': 'DEMO-20250903-210000-ABCD1234',
            'plan_id': plan_id,
            'original_request': '点击左上角的地址，进入地址选择页后，校验地址选择页搜索框默认文案是否为"搜索城市/区县/地点"。',
            'execution_status': 'success',
            'total_rounds': 12,
            'total_duration': 185.7,
            'start_time': datetime.now().isoformat(),
            'end_time': datetime.now().isoformat()
        }
    
    def _insert_test_execution(self, execution_data: Dict[str, Any]) -> int:
        """插入测试执行数据"""
        if not self.db_manager:
            self.db_manager = get_database_manager(self.config)
            
        insert_sql = """
        INSERT INTO test_executions (
            execution_id, plan_id, original_request, execution_status,
            total_rounds, total_duration, start_time, end_time
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            execution_data['execution_id'],
            execution_data['plan_id'],
            execution_data['original_request'],
            execution_data['execution_status'],
            execution_data['total_rounds'],
            execution_data['total_duration'],
            execution_data['start_time'],
            execution_data['end_time']
        )
        
        return self.db_manager.execute_update(insert_sql, params)
    
    def _insert_sample_tool_executions(self, execution_id: str) -> int:
        """插入示例工具执行数据"""
        if not self.db_manager:
            self.db_manager = get_database_manager(self.config)
            
        # 示例工具执行数据
        tool_executions = [
            {
                'execution_id': execution_id,
                'round_number': 1,
                'tool_name': 'find_available_device',
                'tool_parameters': json.dumps({'platform': 'ios'}),
                'execution_time': 0.5,
                'tool_status': 'success',
                'tool_result': json.dumps({
                    'status': 'success', 
                    'udid': '00008140-000238321401801C',
                    'platform': 'ios'
                }),
                'result_summary': '找到可用的ios设备: 00008140-000238321401801C',
                'image_url': None,
                'local_path': None,
                'error_message': None
            },
            {
                'execution_id': execution_id,
                'round_number': 2,
                'tool_name': 'start_device_test',
                'tool_parameters': json.dumps({'udid': '00008140-000238321401801C'}),
                'execution_time': 3.2,
                'tool_status': 'success',
                'tool_result': json.dumps({
                    'status': 'success',
                    'message': '设备测试环境启动成功'
                }),
                'result_summary': '成功启动设备测试环境',
                'image_url': None,
                'local_path': None,
                'error_message': None
            },
            {
                'execution_id': execution_id,
                'round_number': 3,
                'tool_name': 'find_element_on_page',
                'tool_parameters': json.dumps({
                    'udid': '00008140-000238321401801C',
                    'element': '地址',
                    'scene_desc': '从首页定位地址'
                }),
                'execution_time': 12.8,
                'tool_status': 'success',
                'tool_result': json.dumps({
                    'status': 'success',
                    'found': True,
                    'element': '地址'
                }),
                'result_summary': '元素查找完成: 找到',
                'image_url': 'http://p0.meituan.net/ptautotest/demo_image.png',
                'local_path': '/path/to/screenshot.png',
                'error_message': None
            }
        ]
        
        insert_sql = """
        INSERT INTO tool_executions (
            execution_id, round_number, tool_name, tool_parameters, execution_time,
            tool_status, tool_result, result_summary, image_url, local_path, error_message
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        count = 0
        for tool_data in tool_executions:
            params = (
                tool_data['execution_id'],
                tool_data['round_number'],
                tool_data['tool_name'],
                tool_data['tool_parameters'],
                tool_data['execution_time'],
                tool_data['tool_status'],
                tool_data['tool_result'],
                tool_data['result_summary'],
                tool_data['image_url'],
                tool_data['local_path'],
                tool_data['error_message']
            )
            
            self.db_manager.execute_update(insert_sql, params)
            count += 1
        
        return count
    
    def _insert_sample_evaluations(self, execution_id: str, plan_id: str) -> int:
        """插入示例评价数据"""
        if not self.db_manager:
            self.db_manager = get_database_manager(self.config)
        
        count = 0
        
        # 1. 插入计划评价
        plan_eval_sql = """
        INSERT INTO plan_evaluations (
            execution_id, plan_id, plan_quality_score, plan_analysis_content,
            plan_key_issues, plan_improvement_suggestions, step_completeness_score,
            step_logic_score, parameter_definition_score, redundancy_score, plan_evaluation_notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        plan_eval_params = (
            execution_id, plan_id, 0.85, 
            '测试计划设计合理，步骤清晰，但存在部分冗余操作',
            json.dumps(['步骤过于详细，可能影响执行效率'], ensure_ascii=False),
            json.dumps(['简化非必要的页面分析步骤', '优化参数传递机制'], ensure_ascii=False),
            0.9, 0.8, 0.75, 0.6,
            '计划质量良好，建议进一步优化'
        )
        
        self.db_manager.execute_update(plan_eval_sql, plan_eval_params)
        count += 1
        
        # 2. 插入执行评价
        exec_eval_sql = """
        INSERT INTO execution_evaluations (
            execution_id, execution_compliance_score, execution_quality_score, goal_achievement_score,
            compliance_analysis_content, quality_analysis_content, path_tracking_analysis,
            parameter_handling_score, error_handling_score, step_execution_rate, average_step_success_rate,
            execution_key_issues, execution_improvement_suggestions, execution_evaluation_notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        exec_eval_params = (
            execution_id, 0.92, 0.88, 0.95,
            '执行过程严格按照计划进行，符合度高',
            '工具执行稳定，响应时间合理',
            '执行路径清晰，无异常分支',
            0.9, 0.85, 1.0, 0.95,
            json.dumps(['个别工具执行耗时较长'], ensure_ascii=False),
            json.dumps(['优化网络请求超时设置', '增加重试机制'], ensure_ascii=False),
            '执行质量优秀，达到预期目标'
        )
        
        self.db_manager.execute_update(exec_eval_sql, exec_eval_params)
        count += 1
        
        # 3. 插入综合评价
        comp_eval_sql = """
        INSERT INTO comprehensive_evaluations (
            execution_id, evaluation_round, analysis_time, analysis_model,
            final_success_status, overall_success_score, confidence_score,
            comprehensive_analysis, root_cause_analysis, best_practices_suggestions,
            improvement_directions, business_logic_impact, user_experience_impact,
            evaluation_summary, next_steps_recommendations
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        comp_eval_params = (
            execution_id, 'demo_round_001', datetime.now().isoformat(), 'qwen3-30b-demo',
            'success', 0.90, 0.95,
            '本次测试整体表现优秀，达成了主要测试目标',
            '成功的关键在于计划设计合理，执行过程稳定',
            '建议保持当前的测试流程设计，继续优化工具性能',
            json.dumps(['继续优化工具执行效率', '增强错误处理能力'], ensure_ascii=False),
            '验证了地址选择功能的正确性，保障了用户体验',
            '为用户提供了稳定可靠的地址选择服务',
            '测试成功完成，功能运行正常，用户体验良好',
            '建议将此测试纳入回归测试套件，定期执行验证'
        )
        
        self.db_manager.execute_update(comp_eval_sql, comp_eval_params)
        count += 1
        
        return count
    
    def _execute_sample_queries(self, execution_id: str) -> Dict[str, Any]:
        """执行示例查询"""
        if not self.db_manager:
            self.db_manager = get_database_manager(self.config)
        
        queries_results = {}
        
        # 查询1: 获取完整的测试信息
        query1 = """
        SELECT 
            tp.plan_id, tp.original_request, tp.platform, tp.total_steps,
            te.execution_id, te.execution_status, te.total_rounds, te.total_duration,
            pe.plan_quality_score, ee.execution_quality_score, ce.overall_success_score
        FROM test_executions te
        LEFT JOIN test_plans tp ON te.plan_id = tp.plan_id
        LEFT JOIN plan_evaluations pe ON te.execution_id = pe.execution_id
        LEFT JOIN execution_evaluations ee ON te.execution_id = ee.execution_id
        LEFT JOIN comprehensive_evaluations ce ON te.execution_id = ce.execution_id
        WHERE te.execution_id = ?
        """
        
        results1 = self.db_manager.execute_query(query1, (execution_id,))
        queries_results['complete_test_info'] = [dict(row) for row in results1]
        
        # 查询2: 获取工具执行详情
        query2 = """
        SELECT tool_name, tool_status, execution_time, result_summary
        FROM tool_executions 
        WHERE execution_id = ?
        ORDER BY round_number
        """
        
        results2 = self.db_manager.execute_query(query2, (execution_id,))
        queries_results['tool_execution_details'] = [dict(row) for row in results2]
        
        # 查询3: 获取评价摘要
        query3 = """
        SELECT 
            pe.plan_quality_score, pe.step_completeness_score,
            ee.execution_compliance_score, ee.goal_achievement_score,
            ce.final_success_status, ce.overall_success_score, ce.evaluation_summary
        FROM plan_evaluations pe
        LEFT JOIN execution_evaluations ee ON pe.execution_id = ee.execution_id
        LEFT JOIN comprehensive_evaluations ce ON pe.execution_id = ce.execution_id
        WHERE pe.execution_id = ?
        """
        
        results3 = self.db_manager.execute_query(query3, (execution_id,))
        queries_results['evaluation_summary'] = [dict(row) for row in results3]
        
        # 查询4: 统计信息
        query4_count = "SELECT COUNT(*) as total_executions FROM test_executions"
        query4_avg = "SELECT AVG(total_duration) as avg_duration FROM test_executions WHERE execution_status = 'success'"
        
        count_results = self.db_manager.execute_query(query4_count)
        avg_results = self.db_manager.execute_query(query4_avg)
        
        queries_results['statistics'] = {
            'total_executions': dict(count_results[0])['total_executions'] if count_results else 0,
            'avg_duration': dict(avg_results[0])['avg_duration'] if avg_results else 0
        }
        
        return queries_results
    
    def _display_query_results(self, query_results: Dict[str, Any]) -> None:
        """展示查询结果"""
        
        print("\n" + "="*50)
        print("📊 数据库查询结果展示")
        print("="*50)
        
        # 展示完整测试信息
        if 'complete_test_info' in query_results and query_results['complete_test_info']:
            info = query_results['complete_test_info'][0]
            print(f"\n🎯 测试基本信息:")
            print(f"  计划ID: {info['plan_id']}")
            print(f"  执行ID: {info['execution_id']}")
            print(f"  平台: {info['platform']}")
            print(f"  总步骤: {info['total_steps']}")
            print(f"  执行轮数: {info['total_rounds']}")
            print(f"  执行耗时: {info['total_duration']}秒")
            print(f"  执行状态: {info['execution_status']}")
        
        # 展示工具执行详情
        if 'tool_execution_details' in query_results:
            tools = query_results['tool_execution_details']
            print(f"\n🔧 工具执行详情 (共{len(tools)}个):")
            for i, tool in enumerate(tools, 1):
                print(f"  {i}. {tool['tool_name']} - {tool['tool_status']} ({tool['execution_time']}秒)")
                print(f"     结果: {tool['result_summary']}")
        
        # 展示评价摘要
        if 'evaluation_summary' in query_results and query_results['evaluation_summary']:
            eval_data = query_results['evaluation_summary'][0]
            print(f"\n📈 测试评价摘要:")
            print(f"  计划质量评分: {eval_data['plan_quality_score']}")
            print(f"  执行符合度评分: {eval_data['execution_compliance_score']}")
            print(f"  目标达成度评分: {eval_data['goal_achievement_score']}")
            print(f"  总体成功评分: {eval_data['overall_success_score']}")
            print(f"  最终状态: {eval_data['final_success_status']}")
            print(f"  评价总结: {eval_data['evaluation_summary']}")
        
        # 展示统计信息
        if 'statistics' in query_results:
            stats = query_results['statistics']
            print(f"\n📊 统计信息:")
            print(f"  总执行次数: {stats['total_executions']}")
            print(f"  平均执行时长: {stats['avg_duration']:.2f}秒")
    
    def _get_database_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        if not self.db_manager:
            self.db_manager = get_database_manager(self.config)
        
        stats = {}
        
        # 获取各表的记录数量
        tables = ['test_plans', 'test_executions', 'tool_executions', 
                 'execution_failures', 'plan_evaluations', 'execution_evaluations', 
                 'comprehensive_evaluations']
        
        for table in tables:
            try:
                result = self.db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table}")
                stats[f'{table}_count'] = dict(result[0])['count'] if result else 0
            except:
                stats[f'{table}_count'] = 0
        
        # 获取数据库基本信息
        db_info = self.db_manager.get_database_info()
        stats.update(db_info)
        
        print(f"\n📊 数据库统计信息:")
        print(f"  数据库文件大小: {stats.get('database_size', 0)} bytes")
        print(f"  测试计划数: {stats.get('test_plans_count', 0)}")
        print(f"  测试执行数: {stats.get('test_executions_count', 0)}")
        print(f"  工具执行记录数: {stats.get('tool_executions_count', 0)}")
        print(f"  计划评价数: {stats.get('plan_evaluations_count', 0)}")
        print(f"  执行评价数: {stats.get('execution_evaluations_count', 0)}")
        print(f"  综合评价数: {stats.get('comprehensive_evaluations_count', 0)}")
        
        return stats


def main():
    """主函数 - 运行完整示例"""
    
    # 创建示例实例
    example = DatabaseExample()
    
    try:
        # 运行完整示例
        result = example.run_complete_example()
        
        print(f"\n" + "="*50)
        print(f"🎉 示例运行总结")
        print(f"="*50)
        print(f"✅ 完成步骤数: {len(result['steps_completed'])}")
        print(f"📝 插入数据: {len(result['data_inserted'])} 种类型")
        print(f"🔍 执行查询: {len(result['queries_executed'])} 个")
        
        if result['errors']:
            print(f"⚠️  发现错误: {len(result['errors'])}")
            for error in result['errors']:
                print(f"  - {error}")
        else:
            print(f"🎯 没有发现错误，示例运行完美！")
        
        return result
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        # 清理示例数据库（可选）
        cleanup = input(f"\n🧹 是否删除示例数据库文件？(y/n): ").strip().lower()
        if cleanup == 'y':
            if os.path.exists(example.database_path):
                os.remove(example.database_path)
                print(f"✅ 已删除示例数据库: {example.database_path}")
        else:
            print(f"📁 示例数据库保留: {example.database_path}")


if __name__ == "__main__":
    main()