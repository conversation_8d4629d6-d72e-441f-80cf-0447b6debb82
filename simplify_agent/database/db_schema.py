#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库表结构定义模块
包含所有表的 SQL 创建语句、索引定义和外键约束
"""

import os
import sys
from typing import Dict, List, Optional

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from tools._concurrent_log_manager import get_current_task_log_manager


class DatabaseSchema:
    """数据库表结构管理类"""
    
    # 数据库版本
    DATABASE_VERSION = "1.0.0"
    
    def __init__(self):
        self.tables = {}
        self.indexes = {}
        self.foreign_keys = {}
        self._init_schema()
    
    def _init_schema(self):
        """初始化所有表结构"""
        self._define_core_tables()
        self._define_detail_tables()  
        self._define_evaluation_tables()
        self._define_indexes()
        self._define_foreign_keys()
    
    def _define_core_tables(self):
        """定义核心数据表"""
        
        # 1. 测试计划表
        self.tables['test_plans'] = """
        CREATE TABLE IF NOT EXISTS test_plans (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            plan_id VARCHAR(50) NOT NULL UNIQUE,
            original_request TEXT NOT NULL,
            platform VARCHAR(20) NOT NULL,
            total_steps INTEGER NOT NULL,
            plan_summary TEXT,
            structured_plan TEXT NOT NULL,  -- JSON格式的详细计划步骤
            generation_metadata TEXT,       -- JSON格式的生成元数据(处理时间、模型信息等)
            agent_instructions TEXT,        -- 给Agent的执行指令
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            -- 约束
            CHECK (platform IN ('ios', 'android')),
            CHECK (total_steps > 0)
        )"""
        
        # 2. 测试执行表
        self.tables['test_executions'] = """
        CREATE TABLE IF NOT EXISTS test_executions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            execution_id VARCHAR(100) NOT NULL UNIQUE,
            plan_id VARCHAR(50),
            original_request TEXT NOT NULL,
            execution_status VARCHAR(20) NOT NULL DEFAULT 'running',
            total_rounds INTEGER DEFAULT 0,
            total_duration REAL DEFAULT 0,  -- 执行耗时(秒)
            start_time TIMESTAMP,
            end_time TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            -- 约束
            CHECK (execution_status IN ('running', 'success', 'failed', 'timeout', 'cancelled')),
            CHECK (total_rounds >= 0),
            CHECK (total_duration >= 0)
        )"""
    
    def _define_detail_tables(self):
        """定义详情记录表"""
        
        # 3. 工具执行详情表
        self.tables['tool_executions'] = """
        CREATE TABLE IF NOT EXISTS tool_executions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            execution_id VARCHAR(100) NOT NULL,
            round_number INTEGER NOT NULL,
            tool_name VARCHAR(50) NOT NULL,
            tool_parameters TEXT,           -- JSON格式的工具参数
            execution_time REAL,            -- 工具执行耗时(秒)
            tool_status VARCHAR(20),
            tool_result TEXT,               -- JSON格式的完整工具返回结果
            result_summary TEXT,            -- 结果摘要
            image_url VARCHAR(500),         -- 截图URL (如果有)
            local_path VARCHAR(500),        -- 本地文件路径 (如果有)
            error_message TEXT,             -- 错误信息 (如果有)
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            -- 约束
            CHECK (round_number > 0),
            CHECK (execution_time >= 0 OR execution_time IS NULL),
            CHECK (tool_status IN ('success', 'error', 'timeout', 'cancelled') OR tool_status IS NULL)
        )"""
        
        # 4. 执行失败详情表  
        self.tables['execution_failures'] = """
        CREATE TABLE IF NOT EXISTS execution_failures (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            execution_id VARCHAR(100) NOT NULL,
            round_number INTEGER NOT NULL,
            tool_name VARCHAR(50),
            failure_type VARCHAR(50),       -- 失败类型: timeout, parameter_error, execution_error, etc.
            error_message TEXT,             -- 错误信息
            error_details TEXT,             -- 详细错误信息 (JSON格式)
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            -- 约束
            CHECK (round_number > 0)
        )"""
    
    def _define_evaluation_tables(self):
        """定义评价分析表 - 合并为单一评价表"""
        
        # 5. 统一测试评价表 (合并原来的3张评价表)
        self.tables['test_evaluations'] = """
        CREATE TABLE IF NOT EXISTS test_evaluations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            execution_id VARCHAR(100) NOT NULL UNIQUE,

            -- 基础元数据
            evaluation_round VARCHAR(50),       -- 评价轮次 (如round_000005)
            analysis_time TIMESTAMP,            -- 分析时间
            analysis_model VARCHAR(50),         -- 分析使用的模型
            round_number INTEGER,               -- 轮次编号

            -- 总体评价结果
            final_success_status VARCHAR(20),   -- 最终成功状态
            overall_success_score REAL,         -- 总体成功评分 (0.0-1.0)
            confidence_score REAL,              -- 置信度 (0.0-1.0)

            -- 维度评分 (0.0-1.0)
            plan_quality_score REAL,            -- 计划质量评分
            execution_compliance_score REAL,    -- 执行符合度评分
            execution_quality_score REAL,       -- 执行质量评分
            goal_achievement_score REAL,        -- 目标达成度评分

            -- 测试信息 (JSON格式)
            original_instruction TEXT,          -- 原始指令
            test_plan_json TEXT,                -- 完整测试计划
            execution_summary_json TEXT,        -- 执行摘要

            -- 评价分析内容
            plan_analysis_content TEXT,         -- 计划质量分析
            compliance_analysis_content TEXT,   -- 执行符合度分析
            quality_analysis_content TEXT,      -- 执行质量分析
            comprehensive_analysis TEXT,        -- 综合评价分析

            -- 问题和建议 (JSON数组格式)
            plan_key_issues TEXT,               -- 计划关键问题
            execution_key_issues TEXT,          -- 执行关键问题
            plan_improvement_suggestions TEXT,  -- 计划改进建议
            execution_improvement_suggestions TEXT, -- 执行改进建议

            -- 综合分析
            plan_coverage_assessment TEXT,      -- 计划覆盖度评估
            major_issues_found TEXT,            -- 主要问题 (JSON数组)
            execution_highlights TEXT,          -- 执行亮点
            improvement_priorities TEXT,        -- 改进优先级 (JSON数组)
            final_recommendations TEXT,         -- 最终建议 (JSON数组)
            key_findings TEXT,                  -- 关键发现 (JSON数组)

            -- 评价总结
            evaluation_summary TEXT,            -- 评价总结
            next_steps_recommendations TEXT,    -- 下一步建议

            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

            -- 约束
            CHECK (final_success_status IN ('success', 'failed', 'partial', 'unknown') OR final_success_status IS NULL),
            CHECK (overall_success_score BETWEEN 0.0 AND 1.0 OR overall_success_score IS NULL),
            CHECK (confidence_score BETWEEN 0.0 AND 1.0 OR confidence_score IS NULL),
            CHECK (plan_quality_score BETWEEN 0.0 AND 1.0 OR plan_quality_score IS NULL),
            CHECK (execution_compliance_score BETWEEN 0.0 AND 1.0 OR execution_compliance_score IS NULL),
            CHECK (execution_quality_score BETWEEN 0.0 AND 1.0 OR execution_quality_score IS NULL),
            CHECK (goal_achievement_score BETWEEN 0.0 AND 1.0 OR goal_achievement_score IS NULL)
        )"""

        # 6. 测试点分析表 (新增)
        self.tables['test_point_evaluations'] = """
        CREATE TABLE IF NOT EXISTS test_point_evaluations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            execution_id VARCHAR(100) NOT NULL,
            test_point_name VARCHAR(200) NOT NULL,  -- 测试点名称
            test_point_order INTEGER,               -- 测试点顺序
            achieved BOOLEAN NOT NULL,              -- 是否达成
            brief_analysis TEXT,                    -- 简要分析
            detailed_analysis TEXT,                 -- 详细分析 (可选)
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

            -- 约束
            CHECK (test_point_order >= 0 OR test_point_order IS NULL),

            -- 唯一约束：同一执行ID下的测试点名称不能重复
            UNIQUE(execution_id, test_point_name)
        )"""
    
    def _define_indexes(self):
        """定义数据库索引"""
        
        # test_plans 表索引
        self.indexes['test_plans'] = [
            "CREATE INDEX IF NOT EXISTS idx_test_plans_plan_id ON test_plans(plan_id)",
            "CREATE INDEX IF NOT EXISTS idx_test_plans_platform ON test_plans(platform)", 
            "CREATE INDEX IF NOT EXISTS idx_test_plans_created_at ON test_plans(created_at)"
        ]
        
        # test_executions 表索引
        self.indexes['test_executions'] = [
            "CREATE INDEX IF NOT EXISTS idx_test_executions_execution_id ON test_executions(execution_id)",
            "CREATE INDEX IF NOT EXISTS idx_test_executions_plan_id ON test_executions(plan_id)",
            "CREATE INDEX IF NOT EXISTS idx_test_executions_status ON test_executions(execution_status)",
            "CREATE INDEX IF NOT EXISTS idx_test_executions_created_at ON test_executions(created_at)"
        ]
        
        # tool_executions 表索引
        self.indexes['tool_executions'] = [
            "CREATE INDEX IF NOT EXISTS idx_tool_executions_execution_id ON tool_executions(execution_id)",
            "CREATE INDEX IF NOT EXISTS idx_tool_executions_tool_name ON tool_executions(tool_name)",
            "CREATE INDEX IF NOT EXISTS idx_tool_executions_round ON tool_executions(round_number)",
            "CREATE INDEX IF NOT EXISTS idx_tool_executions_status ON tool_executions(tool_status)"
        ]
        
        # execution_failures 表索引
        self.indexes['execution_failures'] = [
            "CREATE INDEX IF NOT EXISTS idx_execution_failures_execution_id ON execution_failures(execution_id)",
            "CREATE INDEX IF NOT EXISTS idx_execution_failures_failure_type ON execution_failures(failure_type)"
        ]
        
        # test_evaluations 表索引 (合并后的评价表)
        self.indexes['test_evaluations'] = [
            "CREATE INDEX IF NOT EXISTS idx_test_evaluations_execution_id ON test_evaluations(execution_id)",
            "CREATE INDEX IF NOT EXISTS idx_test_evaluations_success_status ON test_evaluations(final_success_status)",
            "CREATE INDEX IF NOT EXISTS idx_test_evaluations_overall_score ON test_evaluations(overall_success_score)",
            "CREATE INDEX IF NOT EXISTS idx_test_evaluations_analysis_time ON test_evaluations(analysis_time)",
            "CREATE INDEX IF NOT EXISTS idx_test_evaluations_round_number ON test_evaluations(round_number)"
        ]

        # test_point_evaluations 表索引
        self.indexes['test_point_evaluations'] = [
            "CREATE INDEX IF NOT EXISTS idx_test_point_evaluations_execution_id ON test_point_evaluations(execution_id)",
            "CREATE INDEX IF NOT EXISTS idx_test_point_evaluations_achieved ON test_point_evaluations(achieved)",
            "CREATE INDEX IF NOT EXISTS idx_test_point_evaluations_order ON test_point_evaluations(test_point_order)"
        ]
    
    def _define_foreign_keys(self):
        """定义外键约束（SQLite 需要在创建表时定义，这里用于记录关系）"""
        
        self.foreign_keys = {
            'test_executions': [
                "FOREIGN KEY (plan_id) REFERENCES test_plans(plan_id)"
            ],
            'tool_executions': [
                "FOREIGN KEY (execution_id) REFERENCES test_executions(execution_id)"
            ],
            'execution_failures': [
                "FOREIGN KEY (execution_id) REFERENCES test_executions(execution_id)"
            ],
            'test_evaluations': [
                "FOREIGN KEY (execution_id) REFERENCES test_executions(execution_id)"
            ],
            'test_point_evaluations': [
                "FOREIGN KEY (execution_id) REFERENCES test_executions(execution_id)"
            ]
        }
    
    def get_table_create_sql(self, table_name: str) -> Optional[str]:
        """获取指定表的创建SQL"""
        return self.tables.get(table_name)
    
    def get_table_indexes_sql(self, table_name: str) -> List[str]:
        """获取指定表的索引创建SQL"""
        return self.indexes.get(table_name, [])
    
    def get_all_tables(self) -> Dict[str, str]:
        """获取所有表的创建SQL"""
        return self.tables.copy()
    
    def get_all_indexes(self) -> Dict[str, List[str]]:
        """获取所有索引的创建SQL"""
        return self.indexes.copy()
    
    def get_foreign_keys(self, table_name: str) -> List[str]:
        """获取指定表的外键约束"""
        return self.foreign_keys.get(table_name, [])
    
    def get_table_order(self) -> List[str]:
        """获取表的创建顺序（按依赖关系排序）"""
        return [
            'test_plans',           # 无依赖，最先创建
            'test_executions',      # 依赖 test_plans
            'tool_executions',      # 依赖 test_executions
            'execution_failures',   # 依赖 test_executions
            'test_evaluations',     # 依赖 test_executions (合并后的评价表)
            'test_point_evaluations'  # 依赖 test_executions
        ]
    
    def validate_schema(self) -> List[str]:
        """验证数据库表结构的完整性"""
        warnings = []
        
        # 检查表是否都有主键
        for table_name, sql in self.tables.items():
            if "PRIMARY KEY" not in sql:
                warnings.append(f"表 {table_name} 缺少主键定义")
        
        # 检查外键关系是否正确
        table_order = self.get_table_order()
        for i, table_name in enumerate(table_order):
            fks = self.get_foreign_keys(table_name)
            for fk in fks:
                # 简单检查外键引用的表是否在当前表之前定义
                ref_table = fk.split("REFERENCES ")[1].split("(")[0].strip()
                if ref_table not in table_order[:i]:
                    warnings.append(f"表 {table_name} 的外键引用 {ref_table} 可能存在依赖顺序问题")
        
        return warnings
    
    def get_schema_info(self) -> Dict[str, any]:
        """获取数据库表结构信息摘要"""
        return {
            'database_version': self.DATABASE_VERSION,
            'total_tables': len(self.tables),
            'table_names': list(self.tables.keys()),
            'total_indexes': sum(len(indexes) for indexes in self.indexes.values()),
            'foreign_key_relationships': len([fk for fks in self.foreign_keys.values() for fk in fks]),
            'creation_order': self.get_table_order(),
            'validation_warnings': self.validate_schema()
        }


# 全局表结构实例
_schema_instance = None

def get_database_schema() -> DatabaseSchema:
    """获取全局数据库表结构实例"""
    global _schema_instance
    if _schema_instance is None:
        _schema_instance = DatabaseSchema()
    return _schema_instance


if __name__ == "__main__":
    """测试数据库表结构定义"""
    
    print("🧪 测试数据库表结构定义...")
    
    try:
        # 创建表结构实例
        schema = get_database_schema()
        
        # 获取表结构信息
        info = schema.get_schema_info()
        print(f"\n📊 数据库表结构信息:")
        print(f"数据库版本: {info['database_version']}")
        print(f"表总数: {info['total_tables']}")
        print(f"索引总数: {info['total_indexes']}")
        print(f"外键关系数: {info['foreign_key_relationships']}")
        
        print(f"\n📋 表创建顺序:")
        for i, table_name in enumerate(info['creation_order'], 1):
            print(f"{i}. {table_name}")
        
        # 验证表结构
        warnings = info['validation_warnings']
        if warnings:
            print(f"\n⚠️  结构验证警告:")
            for warning in warnings:
                print(f"- {warning}")
        else:
            print(f"\n✅ 表结构验证通过")
        
        # 显示一个表的示例SQL
        print(f"\n📄 test_plans 表创建SQL示例:")
        sql = schema.get_table_create_sql('test_plans')
        if sql:
            # 格式化显示前几行
            lines = sql.strip().split('\n')[:10]
            for line in lines:
                print(f"  {line}")
            if len(sql.strip().split('\n')) > 10:
                print("  ...")
        
        # 显示索引示例
        indexes = schema.get_table_indexes_sql('test_plans')
        if indexes:
            print(f"\n🗂️  test_plans 表索引:")
            for idx in indexes:
                print(f"  {idx}")
        
        print(f"\n✅ 数据库表结构定义模块测试通过!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()