#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库初始化脚本
负责创建数据库文件、表结构、索引和基础配置
"""

import os
import sys
import time
from typing import Optional, Dict, Any, List

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from simplify_agent.database.db_connection import (
    DatabaseManager, DatabaseConfig, get_database_manager, init_database_manager
)
from simplify_agent.database.db_schema import get_database_schema
from tools._concurrent_log_manager import get_current_task_log_manager


class DatabaseInitializer:
    """数据库初始化器"""

    def __init__(self, config=None):
        # 处理不同类型的配置对象
        if config is None:
            self.config = DatabaseConfig()
        elif hasattr(config, 'database_path'):
            # 如果是 DatabaseConfig 对象
            self.config = config
        elif hasattr(config, 'get_database_config'):
            # 如果是 LogServerConfig 对象
            db_config = config.get_database_config()
            self.config = DatabaseConfig(
                database_path=db_config['path'],
                connection_timeout=db_config.get('timeout', 30),
                max_connections=db_config.get('connection_pool_size', 10),
                enable_foreign_keys=True,
                journal_mode='WAL',
                synchronous='NORMAL'
            )
        else:
            # 默认配置
            self.config = DatabaseConfig()

        self.schema = get_database_schema()
        self.db_manager = None
        
    def initialize_database(self, force_recreate: bool = False) -> Dict[str, Any]:
        """
        初始化数据库
        
        Args:
            force_recreate: 是否强制重新创建数据库
            
        Returns:
            初始化结果信息
        """
        start_time = time.time()
        
        try:
            get_current_task_log_manager().info_tools("开始初始化数据库...", "DatabaseInitializer")
            
            # 检查是否需要重新创建
            if force_recreate and os.path.exists(self.config.database_path):
                get_current_task_log_manager().warning_tools(
                    f"强制重新创建数据库，删除现有文件: {self.config.database_path}", 
                    "DatabaseInitializer"
                )
                os.remove(self.config.database_path)
            
            # 初始化数据库管理器
            self.db_manager = init_database_manager(self.config)
            
            # 检查数据库状态
            db_status = self._check_database_status()
            
            init_result = {
                'status': 'success',
                'database_path': self.config.database_path,
                'database_existed': db_status['existed'],
                'tables_created': 0,
                'indexes_created': 0,
                'initialization_time': 0,
                'errors': []
            }
            
            # 创建表结构
            if not db_status['complete'] or force_recreate:
                table_result = self._create_tables()
                init_result.update(table_result)
                
                # 创建索引
                index_result = self._create_indexes() 
                init_result['indexes_created'] = index_result['indexes_created']
                init_result['errors'].extend(index_result['errors'])
                
                # 验证数据库完整性
                validation_result = self._validate_database()
                if not validation_result['valid']:
                    init_result['errors'].extend(validation_result['errors'])
                    
            else:
                get_current_task_log_manager().info_tools("数据库已存在且完整，跳过创建", "DatabaseInitializer")
                init_result['tables_created'] = len(self.schema.get_all_tables())
                init_result['indexes_created'] = sum(len(idxs) for idxs in self.schema.get_all_indexes().values())
            
            init_result['initialization_time'] = round(time.time() - start_time, 4)
            
            # 执行健康检查
            health_result = self.db_manager.health_check()
            init_result['health_check'] = health_result
            
            if init_result['errors']:
                init_result['status'] = 'warning'
                get_current_task_log_manager().warning_tools(
                    f"数据库初始化完成但有 {len(init_result['errors'])} 个警告", 
                    "DatabaseInitializer"
                )
            else:
                get_current_task_log_manager().info_tools(
                    f"数据库初始化成功，耗时: {init_result['initialization_time']}秒", 
                    "DatabaseInitializer"
                )
            
            return init_result
            
        except Exception as e:
            error_msg = f"数据库初始化失败: {e}"
            get_current_task_log_manager().error_tools(error_msg, "DatabaseInitializer")
            
            return {
                'status': 'error',
                'error': str(e),
                'initialization_time': round(time.time() - start_time, 4),
                'database_path': self.config.database_path
            }
    
    def _check_database_status(self) -> Dict[str, Any]:
        """检查数据库当前状态"""
        try:
            status = {
                'existed': os.path.exists(self.config.database_path),
                'complete': False,
                'existing_tables': [],
                'missing_tables': []
            }
            
            if status['existed']:
                # 检查表是否都存在
                expected_tables = set(self.schema.get_table_order())
                existing_tables = set()
                
                for table_name in expected_tables:
                    if self.db_manager.table_exists(table_name):
                        existing_tables.add(table_name)
                        status['existing_tables'].append(table_name)
                    else:
                        status['missing_tables'].append(table_name)
                
                status['complete'] = expected_tables == existing_tables
                
                get_current_task_log_manager().info_tools(
                    f"数据库状态检查: 存在={status['existed']}, 完整={status['complete']}, "
                    f"现有表={len(status['existing_tables'])}, 缺失表={len(status['missing_tables'])}", 
                    "DatabaseInitializer"
                )
            
            return status
            
        except Exception as e:
            get_current_task_log_manager().error_tools(f"检查数据库状态失败: {e}", "DatabaseInitializer")
            return {
                'existed': False,
                'complete': False, 
                'existing_tables': [],
                'missing_tables': list(self.schema.get_table_order()),
                'error': str(e)
            }
    
    def _create_tables(self) -> Dict[str, Any]:
        """创建数据库表"""
        result = {
            'tables_created': 0,
            'tables_failed': 0,
            'errors': []
        }
        
        try:
            # 按依赖顺序创建表
            table_order = self.schema.get_table_order()
            
            for table_name in table_order:
                try:
                    # 获取表创建SQL
                    create_sql = self.schema.get_table_create_sql(table_name)
                    if not create_sql:
                        error_msg = f"未找到表 {table_name} 的创建SQL"
                        result['errors'].append(error_msg)
                        result['tables_failed'] += 1
                        continue
                    
                    # 执行表创建
                    self.db_manager.execute_update(create_sql)
                    result['tables_created'] += 1
                    
                    get_current_task_log_manager().info_tools(f"成功创建表: {table_name}", "DatabaseInitializer")
                    
                except Exception as e:
                    error_msg = f"创建表 {table_name} 失败: {e}"
                    result['errors'].append(error_msg)
                    result['tables_failed'] += 1
                    get_current_task_log_manager().error_tools(error_msg, "DatabaseInitializer")
            
            get_current_task_log_manager().info_tools(
                f"表创建完成: 成功={result['tables_created']}, 失败={result['tables_failed']}", 
                "DatabaseInitializer"
            )
            
            return result
            
        except Exception as e:
            error_msg = f"创建表过程失败: {e}"
            result['errors'].append(error_msg)
            get_current_task_log_manager().error_tools(error_msg, "DatabaseInitializer")
            return result
    
    def _create_indexes(self) -> Dict[str, Any]:
        """创建数据库索引"""
        result = {
            'indexes_created': 0,
            'indexes_failed': 0,
            'errors': []
        }
        
        try:
            all_indexes = self.schema.get_all_indexes()
            
            for table_name, index_sqls in all_indexes.items():
                for index_sql in index_sqls:
                    try:
                        self.db_manager.execute_update(index_sql)
                        result['indexes_created'] += 1
                        
                    except Exception as e:
                        error_msg = f"创建索引失败: {index_sql[:50]}..., 错误: {e}"
                        result['errors'].append(error_msg)
                        result['indexes_failed'] += 1
                        get_current_task_log_manager().warning_tools(error_msg, "DatabaseInitializer")
            
            get_current_task_log_manager().info_tools(
                f"索引创建完成: 成功={result['indexes_created']}, 失败={result['indexes_failed']}", 
                "DatabaseInitializer"
            )
            
            return result
            
        except Exception as e:
            error_msg = f"创建索引过程失败: {e}"
            result['errors'].append(error_msg)
            get_current_task_log_manager().error_tools(error_msg, "DatabaseInitializer")
            return result
    
    def _validate_database(self) -> Dict[str, Any]:
        """验证数据库完整性"""
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            # 检查所有表是否存在
            expected_tables = self.schema.get_table_order()
            for table_name in expected_tables:
                if not self.db_manager.table_exists(table_name):
                    error_msg = f"表 {table_name} 不存在"
                    result['errors'].append(error_msg)
                    result['valid'] = False
            
            # 检查表结构
            for table_name in expected_tables:
                if self.db_manager.table_exists(table_name):
                    try:
                        table_info = self.db_manager.get_table_info(table_name)
                        if not table_info:
                            warning_msg = f"表 {table_name} 结构信息为空"
                            result['warnings'].append(warning_msg)
                    except Exception as e:
                        error_msg = f"检查表 {table_name} 结构失败: {e}"
                        result['errors'].append(error_msg)
            
            # 验证表结构设计
            schema_warnings = self.schema.validate_schema()
            result['warnings'].extend(schema_warnings)
            
            # 执行基本查询测试
            try:
                self.db_manager.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
            except Exception as e:
                error_msg = f"基本查询测试失败: {e}"
                result['errors'].append(error_msg)
                result['valid'] = False
            
            get_current_task_log_manager().info_tools(
                f"数据库验证完成: 有效={result['valid']}, 错误={len(result['errors'])}, 警告={len(result['warnings'])}", 
                "DatabaseInitializer"
            )
            
            return result
            
        except Exception as e:
            error_msg = f"数据库验证失败: {e}"
            result['errors'].append(error_msg)
            result['valid'] = False
            get_current_task_log_manager().error_tools(error_msg, "DatabaseInitializer")
            return result
    
    def reset_database(self) -> Dict[str, Any]:
        """重置数据库（删除所有数据但保留结构）"""
        try:
            get_current_task_log_manager().warning_tools("开始重置数据库数据...", "DatabaseInitializer")
            
            # 确保数据库管理器已初始化
            if not self.db_manager:
                self.db_manager = get_database_manager(self.config)
            
            reset_result = {
                'status': 'success',
                'tables_cleared': 0,
                'errors': []
            }
            
            # 按反向依赖顺序清空表（避免外键约束问题）
            table_order = list(reversed(self.schema.get_table_order()))
            
            for table_name in table_order:
                try:
                    if self.db_manager.table_exists(table_name):
                        self.db_manager.execute_update(f"DELETE FROM {table_name}")
                        reset_result['tables_cleared'] += 1
                        get_current_task_log_manager().info_tools(f"已清空表: {table_name}", "DatabaseInitializer")
                
                except Exception as e:
                    error_msg = f"清空表 {table_name} 失败: {e}"
                    reset_result['errors'].append(error_msg)
                    get_current_task_log_manager().error_tools(error_msg, "DatabaseInitializer")
            
            get_current_task_log_manager().info_tools(f"数据库重置完成，清空 {reset_result['tables_cleared']} 个表", "DatabaseInitializer")
            
            if reset_result['errors']:
                reset_result['status'] = 'warning'
                
            return reset_result
            
        except Exception as e:
            error_msg = f"重置数据库失败: {e}"
            get_current_task_log_manager().error_tools(error_msg, "DatabaseInitializer")
            
            return {
                'status': 'error',
                'error': str(e),
                'tables_cleared': 0
            }
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库详细信息"""
        try:
            if not self.db_manager:
                self.db_manager = get_database_manager(self.config)
            
            # 获取基本信息
            basic_info = self.db_manager.get_database_info()
            
            # 获取表结构信息
            schema_info = self.schema.get_schema_info()
            
            # 合并信息
            database_info = {
                'basic_info': basic_info,
                'schema_info': schema_info,
                'config': {
                    'database_path': self.config.database_path,
                    'max_connections': self.config.max_connections,
                    'connection_timeout': self.config.connection_timeout,
                    'foreign_keys_enabled': self.config.enable_foreign_keys,
                    'journal_mode': self.config.journal_mode
                }
            }
            
            return database_info
            
        except Exception as e:
            get_current_task_log_manager().error_tools(f"获取数据库信息失败: {e}", "DatabaseInitializer")
            return {'error': str(e)}


def initialize_simplify_agent_database(
    database_path: Optional[str] = None,
    force_recreate: bool = False
) -> Dict[str, Any]:
    """
    初始化 SimplifyAgent 数据库的便捷函数
    
    Args:
        database_path: 数据库文件路径，None 使用默认路径
        force_recreate: 是否强制重新创建
        
    Returns:
        初始化结果
    """
    config = DatabaseConfig()
    if database_path:
        config.database_path = database_path
    
    initializer = DatabaseInitializer(config)
    return initializer.initialize_database(force_recreate)


if __name__ == "__main__":
    """测试数据库初始化"""
    
    print("🧪 测试数据库初始化...")
    
    try:
        # 使用主数据库路径
        test_db_path = 'simplify_agent/data/simplify_agent.db'
        
        # 删除测试数据库（如果存在）
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
            print(f"📂 删除现有测试数据库: {test_db_path}")
        
        # 初始化数据库
        print("\n🏗️  开始初始化数据库...")
        init_result = initialize_simplify_agent_database(
            database_path=test_db_path,
            force_recreate=True
        )
        
        print(f"\n📊 初始化结果:")
        print(f"状态: {init_result['status']}")
        print(f"数据库路径: {init_result['database_path']}")
        print(f"创建表数: {init_result['tables_created']}")
        print(f"创建索引数: {init_result['indexes_created']}")
        print(f"初始化耗时: {init_result['initialization_time']}秒")
        
        if init_result['errors']:
            print(f"\n⚠️  发现 {len(init_result['errors'])} 个错误:")
            for error in init_result['errors'][:5]:  # 只显示前5个
                print(f"  - {error}")
        
        # 健康检查
        health = init_result.get('health_check', {})
        if health.get('status') == 'healthy':
            print(f"\n✅ 数据库健康检查通过，响应时间: {health.get('response_time', 0)}秒")
        else:
            print(f"\n❌ 数据库健康检查失败")
        
        # 测试重置功能
        print(f"\n🔄 测试数据库重置功能...")
        initializer = DatabaseInitializer(DatabaseConfig(database_path=test_db_path))
        reset_result = initializer.reset_database()
        print(f"重置结果: {reset_result['status']}, 清空表数: {reset_result.get('tables_cleared', 0)}")
        
        # 获取数据库信息
        print(f"\n📋 获取数据库信息...")
        db_info = initializer.get_database_info()
        basic_info = db_info.get('basic_info', {})
        schema_info = db_info.get('schema_info', {})
        
        print(f"数据库大小: {basic_info.get('database_size', 0)} bytes")
        print(f"表总数: {schema_info.get('total_tables', 0)}")
        print(f"数据库版本: {schema_info.get('database_version', 'unknown')}")
        
        print(f"\n✅ 数据库初始化模块测试通过!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 清理测试数据库
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
            print(f"\n🧹 清理测试数据库: {test_db_path}")