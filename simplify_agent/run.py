#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SimplifyAgent Web管理系统简化启动脚本
一键启动前后端服务，支持局域网访问
"""

import os
import sys
import subprocess
import time
import signal
import threading
import psutil
import atexit
import socket
from pathlib import Path

# 配置信息
CONFIG = {
    'HOST_IP': '************',  # 局域网IP地址 (en0网口)
    'BACKEND_PORT': 5003,
    'FRONTEND_PORT': 3000,
    'PROJECT_ROOT': Path(__file__).parent,
    'processes': [],
    'process_pids': [],
    'shutdown_event': threading.Event()
}

def is_port_occupied(port):
    """检查端口是否被占用
    
    Args:
        port (int): 端口号
        
    Returns:
        tuple: (is_occupied, pid_list) - 是否被占用和占用进程PID列表
    """
    try:
        result = subprocess.run(
            f"lsof -t -i:{port}", 
            shell=True, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE, 
            text=True
        )
        if result.returncode == 0 and result.stdout.strip():
            pids = [pid.strip() for pid in result.stdout.strip().split('\n') if pid.strip()]
            return True, pids
        return False, []
    except Exception as e:
        print(f"⚠️  检查端口 {port} 时出错: {e}")
        return False, []

def check_and_kill_port_process(port, service_name):
    """检查端口占用并强制终止占用进程
    
    Args:
        port (int): 端口号
        service_name (str): 服务名称（用于日志显示）
        
    Returns:
        bool: 是否成功清理端口
    """
    print(f"🔍 检查{service_name}端口 {port} 占用情况...")
    
    is_occupied, pids = is_port_occupied(port)
    if not is_occupied:
        print(f"✅ 端口 {port} 未被占用")
        return True
    
    print(f"⚠️  端口 {port} 被占用，发现 {len(pids)} 个进程: {', '.join(pids)}")
    
    # 强制终止所有占用进程
    success_count = 0
    for pid in pids:
        try:
            pid_int = int(pid)
            if psutil.pid_exists(pid_int):
                print(f"   正在终止进程 PID: {pid}")
                kill_process_tree(pid_int)
                success_count += 1
            else:
                print(f"   进程 PID {pid} 不存在")
        except Exception as e:
            print(f"   终止进程 PID {pid} 失败: {e}")
    
    # 等待进程完全退出
    time.sleep(2)
    
    # 再次检查端口状态
    is_still_occupied, remaining_pids = is_port_occupied(port)
    if is_still_occupied:
        print(f"❌ 端口 {port} 仍被占用，剩余进程: {', '.join(remaining_pids)}")
        return False
    else:
        print(f"✅ 端口 {port} 已成功清理")
        return True

def print_banner():
    """打印启动横幅"""
    print(f"""
╔══════════════════════════════════════════════════════════════╗
║                SimplifyAgent Web管理系统                      ║
║                      一键启动脚本                             ║
╠══════════════════════════════════════════════════════════════╣
║  后端地址: http://{CONFIG['HOST_IP']}:{CONFIG['BACKEND_PORT']}                       ║
║  前端地址: http://{CONFIG['HOST_IP']}:{CONFIG['FRONTEND_PORT']}                        ║
║  局域网IP: {CONFIG['HOST_IP']} (en0网口)                          ║
╚══════════════════════════════════════════════════════════════╝
""")

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    # 检查并清理后端端口
    if not check_and_kill_port_process(CONFIG['BACKEND_PORT'], "后端"):
        print("❌ 后端端口清理失败，无法启动服务")
        return False
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PORT'] = str(CONFIG['BACKEND_PORT'])
        env['FLASK_ENV'] = 'production'
        
        # 启动Flask应用 - 使用进程组
        process = subprocess.Popen(
            [sys.executable, '-m', 'web_backend.app'],
            cwd=CONFIG['PROJECT_ROOT'],
            env=env,
            preexec_fn=os.setsid if hasattr(os, 'setsid') else None
        )
        
        CONFIG['processes'].append(('backend', process))
        CONFIG['process_pids'].append(process.pid)
        
        # 等待后端启动
        time.sleep(3)
        
        if process.poll() is None:
            print(f"✅ 后端服务已启动 - http://{CONFIG['HOST_IP']}:{CONFIG['BACKEND_PORT']}")
            return True
        else:
            print("❌ 后端服务启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 启动后端服务失败: {e}")
        return False

def start_frontend():
    """启动前端服务"""
    print("🚀 启动前端服务...")
    
    # 检查并清理前端端口
    if not check_and_kill_port_process(CONFIG['FRONTEND_PORT'], "前端"):
        print("❌ 前端端口清理失败，无法启动服务")
        return False
    
    try:
        frontend_path = CONFIG['PROJECT_ROOT'] / 'web_frontend'
        
        # 启动前端开发服务器 - 使用进程组
        process = subprocess.Popen(
            ['npm', 'run', 'dev'],
            cwd=frontend_path,
            preexec_fn=os.setsid if hasattr(os, 'setsid') else None
        )
        
        CONFIG['processes'].append(('frontend', process))
        CONFIG['process_pids'].append(process.pid)
        
        # 等待前端启动
        time.sleep(8)
        
        if process.poll() is None:
            print(f"✅ 前端服务已启动 - http://{CONFIG['HOST_IP']}:{CONFIG['FRONTEND_PORT']}")
            return True
        else:
            print("❌ 前端服务启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 启动前端服务失败: {e}")
        return False

def print_status():
    """打印服务状态和使用说明"""
    print(f"""
{"="*60}
🎉 SimplifyAgent Web管理系统启动成功!
{"="*60}
📱 前端访问地址: 
   http://localhost:{CONFIG['FRONTEND_PORT']}
   http://127.0.0.1:{CONFIG['FRONTEND_PORT']}
   http://{CONFIG['HOST_IP']}:{CONFIG['FRONTEND_PORT']}
🔧 后端API地址:  http://{CONFIG['HOST_IP']}:{CONFIG['BACKEND_PORT']}
🌐 局域网IP地址:  {CONFIG['HOST_IP']} (en0网口)
{"="*60}
📋 功能模块:
   👤 用户模式: 测试记录、统计分析、执行详情
   🛠️  开发者模式: 数据库管理、SQL执行 (/dev/login)
{"="*60}
⚠️  使用说明:
   • 前端支持多地址访问: localhost, 127.0.0.1, {CONFIG['HOST_IP']}
   • 确保防火墙允许端口 {CONFIG['FRONTEND_PORT']} 和 {CONFIG['BACKEND_PORT']}
   • 局域网内设备可通过上述地址访问
   • 按 Ctrl+C 停止所有服务
{"="*60}
""")

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n🛑 接收到停止信号，正在关闭服务...")
    CONFIG['shutdown_event'].set()

def kill_process_tree(pid):
    """递归终止进程及其所有子进程"""
    try:
        parent = psutil.Process(pid)
        children = parent.children(recursive=True)
        
        # 首先终止所有子进程
        for child in children:
            try:
                print(f"     终止子进程 PID: {child.pid}")
                child.terminate()
            except psutil.NoSuchProcess:
                pass
        
        # 等待子进程退出
        gone, still_alive = psutil.wait_procs(children, timeout=3)
        
        # 强制杀死仍然存在的子进程
        for p in still_alive:
            try:
                print(f"     强制终止子进程 PID: {p.pid}")
                p.kill()
            except psutil.NoSuchProcess:
                pass
        
        # 最后终止父进程
        try:
            print(f"     终止父进程 PID: {parent.pid}")
            parent.terminate()
            parent.wait(timeout=5)
        except psutil.TimeoutExpired:
            print(f"     强制终止父进程 PID: {parent.pid}")
            parent.kill()
        except psutil.NoSuchProcess:
            pass
            
    except psutil.NoSuchProcess:
        print(f"     进程 PID {pid} 不存在")
    except Exception as e:
        print(f"     终止进程 PID {pid} 时出错: {e}")


def cleanup_processes_by_name():
    """通过进程名清理可能残留的进程 - 只清理项目相关进程"""
    project_path = str(CONFIG['PROJECT_ROOT'])
    
    killed_any = False
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if not proc.info['cmdline']:
                continue
                
            cmdline_str = ' '.join(proc.info['cmdline'])
            
            # 只清理包含项目路径的进程
            if project_path not in cmdline_str:
                continue
                
            # 检查是否是我们要清理的进程类型
            should_kill = False
            reason = ""
            
            if 'web_backend.app' in cmdline_str:
                should_kill = True
                reason = "后端服务"
            elif 'simplify_agent/web_frontend' in cmdline_str and ('vite' in cmdline_str or 'npm run dev' in cmdline_str):
                should_kill = True  
                reason = "前端服务"
            elif 'simplify_agent/web_frontend' in cmdline_str and 'node' in cmdline_str and 'vite' in cmdline_str:
                should_kill = True
                reason = "Vite进程"
            
            if should_kill and 'run.py' not in cmdline_str:
                print(f"     发现残留进程: {proc.info['name']} (PID: {proc.info['pid']}) - {reason}")
                proc.terminate()
                killed_any = True
                        
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if killed_any:
        print("     等待残留进程退出...")
        time.sleep(2)
    else:
        print("     没有发现残留的项目进程")


def cleanup():
    """清理进程 - 增强版"""
    print("\n🧹 清理进程...")
    
    # 方法1: 通过保存的进程对象清理
    for name, process in CONFIG['processes']:
        try:
            if process.poll() is None:
                print(f"   停止{name}服务 (PID: {process.pid})")
                kill_process_tree(process.pid)
                print(f"   ✅ {name}服务已停止")
            else:
                print(f"   {name}服务已停止")
        except Exception as e:
            print(f"   ⚠️  停止{name}服务时出错: {e}")
    
    # 方法2: 通过PID清理
    for pid in CONFIG['process_pids']:
        try:
            if psutil.pid_exists(pid):
                print(f"   清理残留进程 PID: {pid}")
                kill_process_tree(pid)
        except Exception as e:
            print(f"   清理 PID {pid} 时出错: {e}")
    
    # 方法3: 通过进程名清理可能的残留进程
    print("   检查并清理残留进程...")
    cleanup_processes_by_name()
    
    # 清空进程列表
    CONFIG['processes'].clear()
    CONFIG['process_pids'].clear()
    
    print("✅ 所有服务已停止")

def check_vite_config():
    """检查Vite配置"""
    print("🔍 检查前端配置...")
    
    vite_config_path = CONFIG['PROJECT_ROOT'] / 'web_frontend' / 'vite.config.ts'
    
    if not vite_config_path.exists():
        print("❌ vite.config.ts 不存在")
        return False
    
    try:
        with open(vite_config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含正确的host配置 (支持多地址访问)
        if "host: '0.0.0.0'" in content or f"host: '{CONFIG['HOST_IP']}'" in content:
            print("✅ Vite配置检查通过")
            return True
        else:
            print(f"""
⚠️  Vite配置需要手动调整
请在 vite.config.ts 的 server 配置中设置:
  server: {{
    port: {CONFIG['FRONTEND_PORT']},
    host: '0.0.0.0',
    proxy: {{
      '/api': {{
        target: 'http://{CONFIG['HOST_IP']}:{CONFIG['BACKEND_PORT']}',
        changeOrigin: true,
        secure: false,
      }},
    }},
  }}
""")
            return True  # 继续启动，但给出警告
    
    except Exception as e:
        print(f"❌ 检查Vite配置失败: {e}")
        return False

def main():
    """主函数"""
    # 注册退出处理器
    atexit.register(cleanup)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 在某些系统上也注册其他信号
    try:
        signal.signal(signal.SIGHUP, signal_handler)
        signal.signal(signal.SIGQUIT, signal_handler)
    except AttributeError:
        # Windows 系统可能没有这些信号
        pass
    
    try:
        print_banner()
        
        # 启动前先清理可能存在的残留进程
        print("🧹 启动前清理检查...")
        cleanup_processes_by_name()
        
        # 检查配置
        check_vite_config()
        
        # 启动后端
        if not start_backend():
            print("❌ 后端服务启动失败")
            cleanup()
            sys.exit(1)
        
        # 启动前端
        if not start_frontend():
            print("❌ 前端服务启动失败")
            cleanup()
            sys.exit(1)
        
        # 打印状态信息
        print_status()
        
        # 等待停止信号
        CONFIG['shutdown_event'].wait()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断，正在关闭服务...")
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
    finally:
        cleanup()

if __name__ == '__main__':
    main()